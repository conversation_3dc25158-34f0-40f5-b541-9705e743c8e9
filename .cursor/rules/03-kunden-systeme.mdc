---
description: <PERSON><PERSON>stamm<PERSON>n, Kontingente und Dokumentation
globs: **/kunden/**
alwaysApply: false
---
# Kunden-Systeme

Zentrale Verwaltung aller kundenbezogenen Daten und Funktionen.

## Stammdaten-System

### Kernkonzepte
- **Kunde**: Zentrale Entität mit Stammdaten (Name, Standard-Stundenpreis, Standard-Anfahrtskosten)
- **Standorte**: Mehrere Adressen pro Kunde, einer als Hauptstandort
- **Ansprechpartner**: Mehrere Kontakte pro Kunde, einer als Hauptansprechpartner

### Wichtige Geschäftsregeln
- Maximal ein Hauptstandort und ein Hauptansprechpartner pro Kunde
- Löschschutz: Kunden mit verknüpften Leistungen/Kontingenten können nicht gelöscht werden
- Automatische Hauptkontakt-Zuweisung bei Erstellung

## Kontingente-System

### Kernkonzepte
- **Kontingent**: De<PERSON><PERSON><PERSON> Stundenumfang mit Gültigkeitszeitraum
- **Verbrauchte Stunden**: Automatische Aktualisierung durch Leistungserfassung
- **Automatische Deaktivierung**: Täglicher Cronjob deaktiviert abgelaufene Kontingente

### Geschäftsregeln
- Standard-Laufzeit: 90 Tage ab Startdatum
- Enddatum muss nach Startdatum liegen
- Nur aktive Kontingente können für Leistungen verwendet werden

## Dokumentations-System

### Kernkonzepte
- **Kategorien**: Vordefinierte Strukturen (Zugangsdaten, Server-Konfiguration, etc.)
- **Felder**: Typisierte Eingabefelder pro Kategorie (Text, Password, URL, etc.)
- **Extrafelder**: Separate Modal-Anzeige für Detailinformationen
- **Einträge**: Ausgefüllte Datensätze pro Kunde und Kategorie

### Konfiguration
- Kategorien werden in `convex/system/dokuKategorienConfig.ts` definiert
- Automatische Synchronisation via `initializeDefaults` Mutation
- Numerische IDs für stabile Referenzierung