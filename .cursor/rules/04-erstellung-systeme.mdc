---
description: Leistungserfassung, Lieferscheine und Übersichten
globs: **/erstellung/**
alwaysApply: false
---
# Erstellungs-Systeme

Systeme für die Erfassung von Leistungen und Erstellung von Dokumenten.

## Leistungserfassungs-System

### Kernkonzepte
- **Leistung**: Erbrachte Dienstleistung mit Start-/Endzeit, Beschreibung und Kostenberechnung
- **Kontingent-Verknüpfung**: Automatische Aktualisierung der verbrauchten Kontingent-Stunden
- **Preis-Hierarchie**: Kundenstandards können pro Leistung überschrieben werden

### Automatisierungen
- **Stundenberechnung**: Automatische Rundung auf 15-Minuten-Intervalle
- **Anfahrtskosten**: Nur bei "Vor-Ort"-Leistungen
- **Kontingent-Updates**: Automatische Synchronisation bei CRUD-Operationen

## Lieferschein-System

### Workflow
1. **Entwurf**: Erststellung ohne Nummer, beliebig bearbeitbar
2. **Leistungen hinzufügen**: Mehrfachauswahl und Zuordnung
3. **Finalisierung**: Vergabe der Lieferschein-Nummer (Format: YYXXXXX)

### Korrektur-System
- **Korrektur-Workflow**: Neue Korrektur → Bearbeitung → Finalisierung
- **Nummerierung**: Original-Nummer mit Versionssuffix (z.B. 2500001.2)
- **Referenzierung**: Korrekturen verweisen immer auf das ursprüngliche Original
- **Markierung**: Originale werden als "korrigiert" markiert

### Geschäftsregeln
- Finalisierte Lieferscheine sind unveränderlich
- Nur Originale können korrigiert werden (keine Korrektur einer Korrektur)
- Erste Korrektur erhält Version .2 (Version .1 wird übersprungen)

## Übersicht-System

### PDF-Generierung
- **Kundenauswahl**: Filter nach Kunde und Zeitraum
- **Leistungs-/Kontingent-Auswahl**: Granulare Kontrolle über Berichtsinhalte
- **Standards-Integration**: Verwendung von Logo und Firmeninformationen
- **Zusammenfassung**: Automatische Berechnung von Gesamtstunden und -kosten