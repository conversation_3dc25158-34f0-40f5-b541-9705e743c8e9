---
description: System-Management, Feedback, Standards, E-Mail und Mitarbeiter
globs: **/system/**, **/verwaltung/**, **/feedback/**
alwaysApply: false
---
# System-Management

Zentrale Verwaltung von systemweiten Einstellungen, Feedback und Mitarbeitern.

## Feedback-System

### Workflow
- **Sammlung**: Über Feedback-Button in der Navigation (gelbes Ausrufezeichen)
- **Kategorisierung**: "feature" oder "bug" Requests
- **Status-Tracking**: "offen", "in_bearbeitung", "erledigt"
- **Verwaltung**: Zentrale Verwaltungsseite unter `/system/feedback`

## Standards-System

### Zweck
Zentrale Definition von systemweiten Standardeinstellungen für PDF-Dokumente.

### Konfiguration
- **Firmeninformationen**: Logo, Adresse, Kontaktdaten
- **PDF-Einstellungen**: Standardwerte für Übersichten und Lieferscheine
- **Logo-Verwaltung**: Upload und Speicherung über Convex Storage

## E-Mail-System

### TurboSMTP-Integration
- **Externe API**: Zuverlässiger Versand über TurboSMTP Gateway
- **PDF-Anhänge**: Clientseitige PDF-Generierung mit React-PDF
- **Empfänger-Management**: Automatische CC-Listen mit Mitarbeitern

### Kritische Implementierungsdetails
```typescript
// Wichtig: Header mit großem 'C'
headers: {
  "Consumerkey": config.consumerKey,     // NICHT "consumerKey"
  "Consumersecret": config.consumerSecret, // NICHT "consumerSecret"
  "Content-Type": "application/json"
}
```

### Anrede-Logik
- **Hauptansprechpartner**: Konsistente Anrede basierend auf Kundenstammdaten
- **Fallback**: Generische Anrede bei fehlendem Hauptansprechpartner

## Mitarbeiter-Verwaltung

### Zweck
- **E-Mail-Integration**: Automatische CC-Listen für Dokument-Versand
- **Leistungserfassung**: Zuordnung von Mitarbeitern zu Leistungen
- **Interne Verwaltung**: Zentrale Mitarbeiterdatenbank

### Datenmodell
```typescript
{
  name: string,
  email: string
}
```

## Doku-Kategorien-System

### Konfigurationsbasiert
- **Definition**: Kategorien in `convex/system/dokuKategorienConfig.ts`
- **Synchronisation**: Automatische Datenbank-Updates via `initializeDefaults`
- **Felder-Typen**: Text, Password, URL, Number, etc.
- **Validierung**: Pflichtfelder und Typen-Validierung