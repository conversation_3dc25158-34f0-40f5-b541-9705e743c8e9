Es sollen alle Dokumentation/Cursor-Rules innerhalb im ".mdc"-Format aus dem ".cursor/rules"-<PERSON>dner initialisiert werden,
sodass eine Übersicht über das gesamte Projekt besteht und speziell definierte Regeln eingehalten werden.

#file: .cursor/rules/01-frontend-architektur.mdc
#file: .cursor/rules/02-auth-und-routing.mdc
#file: .cursor/rules/03-kunden-systeme.mdc
#file: .cursor/rules/04-erstellung-systeme.mdc
#file: .cursor/rules/05-system-management.mdc