# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

**Development:**
- `npm run dev` - Start development server (includes auth check)
- `npm run build` - Production build with TypeScript compilation
- `npm run lint` - Run Biome linter
- `npm run format` - Format code with Biome
- `npm run check` - Run Biome checks

**Testing/Quality:**
- Use Biome for all linting and formatting (not ESLint/Prettier)
- Run `npm run build` to verify TypeScript compilation
- Authentication check runs automatically via `scripts/check-auth.js`

## Architecture Overview

This is a React + Convex full-stack application for German business management ("8IT-Verwaltung"). The stack includes:

- **Frontend**: React 19, TypeScript, Vite, React Router v7, Clerk auth, Shadcn UI, Tailwind CSS
- **Backend**: Convex BaaS with real-time database
- **Key Libraries**: React PDF (reports), ExcelJS (exports), React Hook Form + Zod (forms)
- **Email System**: TurboSMTP API v2 integration with PDF attachments

## Code Organization

**Domain-Driven Structure:**
The application is organized around 5 business domains:
1. **kunden** - Customer management, quotas, documentation
2. **erstellung** - Service entry, delivery notes, reports  
3. **verwaltung** - Employee management
4. **system** - Feedback, standards, documentation categories
5. **startseite** - Dashboard and analytics

**File Patterns:**
- `src/components/[domain]/` - Domain-specific UI components
- `src/pages/[domain]/` - Page components matching routes
- `convex/[domain]/` - Backend functions grouped by domain
- Components follow CRUD pattern: DataTable, FilterControls, Form/PageForm, EmptyState

**Modern CRUD Architecture:**
- **List pages**: Overview with filtering and navigation to detail pages
- **Detail pages**: Dedicated routes for create (`/neu`) and edit (`/:id`) operations
- **Page-based forms**: Replace modals with full-page layouts using `PageLayout` with header actions
- **Compact UI Design**: Professional, information-dense layouts with minimal spacing (space-y-3) and icon-only buttons (40x40px)
- **Examples**: `/kunden/stammdaten/neu`, `/kunden/stammdaten/:id`, `/verwaltung/mitarbeiter/:id`

**Key Directories:**
- `convex/` - Backend database schema and functions
- `src/components/_shared/` - Reusable UI components (Button, Dialog, Table)
- `src/components/layout/` - Layout and navigation components
- `src/lib/utils/` - Utility functions

## Database Schema (Convex)

Core entities and relationships:
- **kunden** → **kunden_kontingente** (quotas) → **kunden_leistungen** (services)
- **kunden_lieferscheine** - Delivery notes with correction support
- **kunden_dokumentation** - Flexible documentation with custom categories
- **mitarbeiter** - Employee data with email integration
- **system_*** - Global configuration tables (email, standards, doku categories)

**Email Integration:**
- Customer contacts with `istMailempfaenger`, `istEmailLieferscheinEmpfaenger` and `istEmailUebersichtEmpfaenger` flags for recipient selection
- Main contact (`istEmailAnrede`) used for email salutation, fallback to `istHauptansprechpartner`
- Employee email addresses for CC distribution
- EmailDialog allows deselecting all customer contacts (employees still required)
- Email templates defined in `convex/system/emailConfig.ts` (redundant templates removed from standardsConfig.ts)

All Convex functions must validate authentication using Clerk integration.

## Development Patterns

**Component Architecture:**
- Use TypeScript interfaces for all props and data
- Follow Shadcn UI component patterns in `_shared/`
- **Page-based CRUD**: Use dedicated pages instead of modals for main operations
- **Header actions**: Use `PageLayout` with `action` prop for Zurück/Speichern/Löschen buttons
- **Compact layouts**: Wide forms with optimized grid structures (4-col for locations, 3-col for contacts)
- **Button Design**: Icon-only buttons (40x40px) with 20x20px icons and tooltips for space efficiency
- **Form Spacing**: Use `space-y-3` instead of `space-y-6` for professional, dense layouts
- Modal dialogs reserved for secondary operations (EmailDialog, quick forms)
- Forms use React Hook Form + Zod validation
- Authentication handled by Clerk with route protection

**Backend (Convex):**
- Database operations through queries/mutations
- All public functions require authentication validation
- Group related functions in domain-specific files
- Use TypeScript for all schema definitions
- **Email system**: External API calls via actions (`"use node";` required)
- **Employee management**: Complete CRUD with `get`, `create`, `update`, `delete_` functions

**Styling:**
- Tailwind CSS with custom dark theme configuration
- Path alias `@/` for clean imports
- Follow existing color scheme and component styling

## Special Considerations

- **Language**: All UI text and comments in German
- **PDF Generation**: Use React PDF for delivery notes and reports
- **Email Integration**: TurboSMTP API v2 with PDF attachments and recipient management
- **Authentication**: Clerk integration with automatic redirects
- **Flexible Documentation**: Custom field system for customer documentation
- **System Standards**: `/system/standards` page displays comprehensive configuration overview with expandable sections for all PDF and email settings

## Email System (TurboSMTP)

**Critical API Requirements:**
- Headers: `"Consumerkey"` and `"Consumersecret"` (capital 'C' required!)
- Payload: Use strings for `from`/`to`, not objects
- PDF attachments: Generate client-side with React PDF, convert to Base64

**Integration Points:**
- Customer contacts: `istEmailLieferscheinEmpfaenger` and `istEmailUebersichtEmpfaenger` flags for type-specific recipient selection
- Main contact: `istHauptansprechpartner` for consistent salutation
- Employee CC: All employees available for CC selection
- PDF generation: React PDF → Blob → ArrayBuffer → Base64

**React Hooks**: Always use same order, use `"skip"` parameter instead of conditional calls

## Data Validation Fixes

**Customer Forms:**
- Form field `stundensatz` (string) → API field `stundenpreis` (float)
- Remove `stundensatz` from API payload to prevent validation errors
- Convert string inputs to numbers: `parseFloat(stundensatz)`

**Employee Management:**
- Use `api.verwaltung.mitarbeiter.*` not `api.kunden.stammdaten.*`
- Backend provides: `list`, `get`, `create`, `update`, `delete_` functions

## Cursor Rules Reference

Comprehensive project guidance consolidated into 5 focused documentation files in `.cursor/rules/`:

- **01-frontend-architektur.mdc** - React architecture, navigation patterns, modal dialogs, UI standards
- **02-auth-und-routing.mdc** - Clerk authentication, route protection, session management  
- **03-kunden-systeme.mdc** - Customer data, quotas, documentation categories
- **04-erstellung-systeme.mdc** - Service entry, delivery notes, overview reports
- **05-system-management.mdc** - Employee management, feedback system, standards, email integration

These rules have been streamlined from 15+ files to 5 thematic groups, removing redundancy while preserving all critical business logic and project-specific patterns.