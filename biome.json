{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false}, "ignore": ["dist", "eslint.config.js", "convex/_generated", ".cursor/rules", "postcss.config.js", "tailwind.config.js", "vite.config.ts", "tsconfig.json", "tsconfig.node.json", "tsconfig.app.json"]}, "javascript": {"formatter": {"quoteStyle": "double"}}, "overrides": [{"include": ["**/*.{ts,tsx}"], "linter": {"rules": {"complexity": {"noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noWith": "off", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error"}, "correctness": {"noConstAssign": "off", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "off", "noSwitchDeclarations": "error", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "warn", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error", "useArrayLiterals": "off", "useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error", "noNamespace": "error", "useAsConstAssertion": "error", "useThrowOnlyError": "error"}, "suspicious": {"noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "off", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noEmptyBlockStatements": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "off", "noGlobalAssign": "error", "noImportAssign": "off", "noMisleadingCharacterClass": "error", "noPrototypeBuiltins": "error", "noRedeclare": "off", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeNegation": "off", "useGetterReturn": "off", "useValidTypeof": "error", "noExplicitAny": "off", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useAwait": "off", "useNamespaceKeyword": "error"}}}}]}