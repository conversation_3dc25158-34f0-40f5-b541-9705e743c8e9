import { v } from "convex/values";
import { internal } from "../_generated/api";
import { Doc, Id } from "../_generated/dataModel";
import { mutation, query } from "../_generated/server";

/**
 * Hilfsfunktion zur Stundenberechnung (Aufrunden auf nächste 15 Min)
 */
const calculateHours = (startZeit: number, endZeit: number): number => {
	if (endZeit <= startZeit) return 0;
	const diffInMinutes = (endZeit - startZeit) / (1000 * 60);
	return Math.ceil(diffInMinutes / 15) / 4;
};

/**
 * Definition des Leistungs-Objekts
 */
const leistungObject = v.object({
	_id: v.id("kunden_leistungen"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	mitarbeiterId: v.id("mitarbeiter"),
	kontingentId: v.id("kunden_kontingente"),
	startZeit: v.number(),
	endZeit: v.number(),
	art: v.string(),
	mitAnfahrt: v.boolean(),
	beschreibung: v.string(),
	stunden: v.number(),
	stundenpreis: v.number(),
	anfahrtskosten: v.number(),
});

/**
 * Definition des erweiterten Leistungs-Objekts mit Namen
 */
const leistungWithNamesObject = v.object({
	_id: v.id("kunden_leistungen"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	mitarbeiterId: v.id("mitarbeiter"),
	kontingentId: v.id("kunden_kontingente"),
	startZeit: v.number(),
	endZeit: v.number(),
	art: v.string(),
	mitAnfahrt: v.boolean(),
	beschreibung: v.string(),
	stunden: v.number(),
	stundenpreis: v.number(),
	anfahrtskosten: v.number(),
	kundeName: v.string(),
	mitarbeiterName: v.string(),
	datum: v.string(),
	kontingentName: v.string(),
	inLieferscheinen: v.optional(v.number()), // Anzahl der Lieferscheine, in denen diese Leistung verwendet wird
});

/**
 * Neue Leistung erstellen
 * - Berechnet Stunden & mitAnfahrt automatisch
 * - Verwendet übergebene Preise oder Kunden-Standardpreise
 * - Prüft & aktualisiert Kontingent
 */
export const create = mutation({
	args: {
		kundenId: v.id("kunden"),
		mitarbeiterId: v.id("mitarbeiter"),
		kontingentId: v.id("kunden_kontingente"),
		startZeit: v.number(),
		endZeit: v.number(),
		art: v.string(), // Erwartet "remote", "vor-Ort", "vor-Ort (free)"
		beschreibung: v.string(),
		stundenpreis: v.optional(v.number()), // Optional, sonst Kunde
		anfahrtskosten: v.optional(v.number()), // Optional, sonst Kunde (nur bei art="vor-Ort")
	},
	returns: v.id("kunden_leistungen"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Zeiten validieren
		if (args.endZeit <= args.startZeit) {
			throw new Error("Endzeit muss nach Startzeit liegen.");
		}

		// Kunde und Kontingent laden
		const kunde = await ctx.db.get(args.kundenId);
		if (!kunde) throw new Error("Kunde nicht gefunden");
		const kontingent = await ctx.db.get(args.kontingentId);
		if (!kontingent) throw new Error("Kontingent nicht gefunden");
		if (!kontingent.istAktiv)
			throw new Error("Ausgewähltes Kontingent ist nicht aktiv.");

		// Stunden berechnen
		const stunden = calculateHours(args.startZeit, args.endZeit);
		if (stunden <= 0) {
			throw new Error("Berechnete Stunden müssen größer als 0 sein.");
		}

		// Prüfen ob Kontingent ausreicht
		const verfuegbareStunden =
			kontingent.stunden - kontingent.verbrauchteStunden;
		if (stunden > verfuegbareStunden) {
			throw new Error(
				`Nicht genügend Stunden im Kontingent (${verfuegbareStunden.toFixed(2)}h verfügbar).`,
			);
		}

		// Preise und Anfahrt bestimmen
		const stundenpreis = args.stundenpreis ?? kunde.stundenpreis;
		const mitAnfahrt = args.art === "vor-Ort";
		const anfahrtskosten = mitAnfahrt
			? (args.anfahrtskosten ?? kunde.anfahrtskosten)
			: 0;

		// Leistung erstellen
		const leistungId = await ctx.db.insert("kunden_leistungen", {
			kundenId: args.kundenId,
			mitarbeiterId: args.mitarbeiterId,
			kontingentId: args.kontingentId,
			startZeit: args.startZeit,
			endZeit: args.endZeit,
			art: args.art,
			mitAnfahrt: mitAnfahrt,
			beschreibung: args.beschreibung,
			stunden: stunden,
			stundenpreis: stundenpreis,
			anfahrtskosten: anfahrtskosten,
		});

		// Kontingent aktualisieren
		await ctx.db.patch(args.kontingentId, {
			verbrauchteStunden: kontingent.verbrauchteStunden + stunden,
		});

		return leistungId;
	},
});

/**
 * Alle Leistungen abrufen (Basisobjekt)
 */
export const getAll = query({
	args: {},
	returns: v.array(leistungObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.query("kunden_leistungen").collect();
	},
});

/**
 * Alle Leistungen mit Kunden-, Mitarbeiter- und Kontingentnamen abrufen
 */
export const list = query({
	args: {},
	// Rückgabetyp anpassen an leistungWithNamesObject
	returns: v.array(leistungWithNamesObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const leistungen = await ctx.db.query("kunden_leistungen").collect();

		const results = [];
		for (const leistung of leistungen) {
			const kunde = await ctx.db.get(leistung.kundenId);
			const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
			const kontingent = await ctx.db.get(leistung.kontingentId); // Kontingent holen

			// Zählen, in wie vielen Lieferscheinen diese Leistung verwendet wird
			const lieferscheinZuordnungen = await ctx.db
				.query("kunden_lieferscheine_zuordnung")
				.withIndex("by_leistung", (q) => q.eq("leistungId", leistung._id))
				.collect();

			// Nur die neuesten Versionen von Lieferscheinen zählen
			const lieferscheinIds = new Set<string>();

			for (const zuordnung of lieferscheinZuordnungen) {
				const lieferschein = await ctx.db.get(zuordnung.lieferscheinId);
				if (!lieferschein) continue;

				// Wenn es ein Original ist, das korrigiert wurde, überspringen
				if (lieferschein.wurdeKorrigiert) continue;

				// Ansonsten hinzufügen (entweder Original ohne Korrektur oder neueste Korrektur)
				lieferscheinIds.add(lieferschein._id.toString());
			}

			results.push({
				...leistung,
				kundeName: kunde?.name || "Unbekannt",
				mitarbeiterName: mitarbeiter?.name || "Unbekannt",
				kontingentName: kontingent?.name || "Unbekannt", // Kontingentname hinzufügen
				datum: new Date(leistung.startZeit).toISOString().split("T")[0],
				inLieferscheinen: lieferscheinIds.size,
			});
		}
		return results;
	},
});

/**
 * Leistung aktualisieren
 * - Passt Stunden/Anfahrt an
 * - Ändert optional Kontingent & passt beide Kontingente an
 */
export const update = mutation({
	args: {
		id: v.id("kunden_leistungen"),
		// Felder, die änderbar sein sollen
		kundenId: v.optional(v.id("kunden")), // Kunde nicht änderbar machen? Ist komplex.
		mitarbeiterId: v.optional(v.id("mitarbeiter")),
		kontingentId: v.optional(v.id("kunden_kontingente")),
		startZeit: v.optional(v.number()),
		endZeit: v.optional(v.number()),
		art: v.optional(v.string()),
		beschreibung: v.optional(v.string()),
		stundenpreis: v.optional(v.number()),
		anfahrtskosten: v.optional(v.number()),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const { id, ...updates } = args;

		// Alte Leistung laden für Vergleiche und alte Kontingent-ID
		const oldLeistung = await ctx.db.get(id);
		if (!oldLeistung) throw new Error("Leistung nicht gefunden.");

		// Neue Daten vorbereiten, beginnend mit den übergebenen Updates
		const newData: Partial<Doc<"kunden_leistungen">> = { ...updates };

		// 1. Stunden neu berechnen, falls Zeiten geändert wurden
		const newStartZeit = updates.startZeit ?? oldLeistung.startZeit;
		const newEndZeit = updates.endZeit ?? oldLeistung.endZeit;
		let newStunden = oldLeistung.stunden;

		if (updates.startZeit !== undefined || updates.endZeit !== undefined) {
			if (newEndZeit <= newStartZeit) {
				throw new Error("Endzeit muss nach Startzeit liegen.");
			}
			newData.stunden = calculateHours(newStartZeit, newEndZeit);
			if (newData.stunden <= 0) {
				throw new Error("Berechnete Stunden müssen größer als 0 sein.");
			}
			newStunden = newData.stunden;
		} else {
			newStunden = oldLeistung.stunden;
		}
		// Wenn newData.stunden nicht explizit gesetzt wurde (weil Zeiten nicht geändert),
		// aber andere Logik es setzen könnte, initialisieren wir es hier nicht erneut.

		// 2. Anfahrt (mitAnfahrt & anfahrtskosten) aktualisieren
		const newArt = updates.art ?? oldLeistung.art;
		const kundeIdForDefaults = updates.kundenId ?? oldLeistung.kundenId; // Kunde für Standardwerte

		if (updates.art !== undefined) {
			newData.mitAnfahrt = newArt === "vor-Ort";
		}
		// Falls mitAnfahrt explizit false gesetzt wird (z.B. durch Art-Änderung)
		if (newData.mitAnfahrt === false) {
			newData.anfahrtskosten = 0;
		} else if (newData.mitAnfahrt === true) {
			// Wenn mitAnfahrt true ist (oder wird)
			if (updates.anfahrtskosten !== undefined) {
				newData.anfahrtskosten = updates.anfahrtskosten;
			} else if (updates.art !== undefined || updates.kundenId !== undefined) {
				// Anfahrtskosten vom Kunden holen, wenn Art zu "vor-Ort" wechselt oder Kunde geändert wird
				// und keine expliziten Anfahrtskosten übergeben wurden.
				const kunde = await ctx.db.get(kundeIdForDefaults);
				newData.anfahrtskosten = kunde?.anfahrtskosten ?? 0;
			}
			// Wenn weder art, kundenId noch anfahrtskosten geändert wurden, bleiben die alten Anfahrtskosten (wenn mitAnfahrt true war)
		}

		// 3. Stundenpreis aktualisieren
		if (updates.stundenpreis !== undefined) {
			newData.stundenpreis = updates.stundenpreis;
		} else if (updates.kundenId !== undefined) {
			// Stundenpreis vom neuen Kunden holen, wenn Kunde geändert und kein expliziter Stundenpreis übergeben.
			const kunde = await ctx.db.get(kundeIdForDefaults);
			newData.stundenpreis = kunde?.stundenpreis ?? 0;
		}
		// Ansonsten bleibt der alte Stundenpreis.

		// 4. Kontingent-Prüfung und Anpassung
		const oldKontingentId = oldLeistung.kontingentId;
		const newKontingentId = updates.kontingentId ?? oldKontingentId;
		const stundenDiff = newStunden - oldLeistung.stunden;

		if (oldKontingentId === newKontingentId) {
			// Gleiches Kontingent: Prüfe, ob Stundenänderung ok ist
			if (stundenDiff !== 0) {
				const kontingent = await ctx.db.get(oldKontingentId);
				if (!kontingent) throw new Error("Kontingent nicht gefunden.");
				if (!kontingent.istAktiv)
					throw new Error("Kontingent ist nicht aktiv.");
				const verfuegbar = kontingent.stunden - kontingent.verbrauchteStunden;
				if (stundenDiff > verfuegbar) {
					throw new Error(
						`Nicht genügend Stunden im Kontingent (${verfuegbar.toFixed(2)}h verfügbar).`,
					);
				}
				await ctx.db.patch(oldKontingentId, {
					verbrauchteStunden: kontingent.verbrauchteStunden + stundenDiff,
				});
			}
		} else {
			// Kontingent gewechselt: Altes entlasten, neues belasten (und prüfen)
			const oldKontingentDoc = await ctx.db.get(oldKontingentId);
			const newKontingentDoc = await ctx.db.get(newKontingentId);

			if (!oldKontingentDoc)
				throw new Error("Altes Kontingent nicht gefunden.");
			if (!newKontingentDoc)
				throw new Error("Neues Kontingent nicht gefunden.");
			if (!newKontingentDoc.istAktiv)
				throw new Error("Neues Kontingent ist nicht aktiv.");

			const verfuegbarNeu =
				newKontingentDoc.stunden - newKontingentDoc.verbrauchteStunden;
			if (newStunden > verfuegbarNeu) {
				throw new Error(
					`Nicht genügend Stunden im neuen Kontingent (${verfuegbarNeu.toFixed(2)}h verfügbar).`,
				);
			}

			// Atomares Update beider Kontingente
			await ctx.db.patch(oldKontingentId, {
				verbrauchteStunden:
					oldKontingentDoc.verbrauchteStunden - oldLeistung.stunden,
			});
			await ctx.db.patch(newKontingentId, {
				verbrauchteStunden: newKontingentDoc.verbrauchteStunden + newStunden,
			});
		}

		// Leistung patchen
		// newData enthält nur die Felder, die tatsächlich geändert werden sollen (oder von args kamen).
		await ctx.db.patch(id, newData);
		return null;
	},
});

/**
 * Leistung löschen
 * - Passt das zugehörige Kontingent an
 */
export const remove = mutation({
	args: {
		id: v.id("kunden_leistungen"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Leistung laden, um Kontingent anzupassen
		const leistung = await ctx.db.get(args.id);
		if (!leistung) {
			console.warn(`Leistung ${args.id} beim Löschen nicht gefunden.`);
			return null; // Oder Fehler werfen?
		}

		// Kontingent anpassen (Stunden zurückgeben)
		const kontingent = await ctx.db.get(leistung.kontingentId);
		if (kontingent) {
			await ctx.db.patch(leistung.kontingentId, {
				verbrauchteStunden: Math.max(
					0,
					kontingent.verbrauchteStunden - leistung.stunden,
				),
			});
		} else {
			console.warn(
				`Kontingent ${leistung.kontingentId} für Leistung ${args.id} nicht gefunden.`,
			);
		}

		// Leistung löschen
		await ctx.db.delete(args.id);
		return null;
	},
});

/**
 * Leistungen nach Kunde abrufen
 */
export const getLeistungenByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kunde", (q) => q.eq("kundenId", args.kundenId))
			.collect();
	},
});

/**
 * Leistungen nach Mitarbeiter abrufen
 */
export const getLeistungenByMitarbeiter = query({
	args: {
		mitarbeiterId: v.id("mitarbeiter"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_mitarbeiter", (q) =>
				q.eq("mitarbeiterId", args.mitarbeiterId),
			)
			.collect();
	},
});

/**
 * Leistungen nach Kontingent abrufen
 */
export const getLeistungenByKontingent = query({
	args: {
		kontingentId: v.id("kunden_kontingente"),
	},
	returns: v.array(leistungObject),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kontingent", (q) =>
				q.eq("kontingentId", args.kontingentId),
			)
			.collect();
	},
});

/**
 * Leistungen nach Kunde abrufen (Alias für getLeistungenByKunde)
 */
export const getByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	returns: v.array(
		v.object({
			_id: v.id("kunden_leistungen"),
			_creationTime: v.number(),
			kundenId: v.id("kunden"),
			mitarbeiterId: v.id("mitarbeiter"),
			kontingentId: v.id("kunden_kontingente"),
			startZeit: v.number(),
			endZeit: v.number(),
			art: v.string(),
			mitAnfahrt: v.boolean(),
			beschreibung: v.string(),
			stunden: v.number(),
			stundenpreis: v.number(),
			anfahrtskosten: v.number(),
			inLieferscheinen: v.optional(v.number()),
			mitarbeiterName: v.optional(v.string()),
			kontingentName: v.optional(v.string()),
		}),
	),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const leistungen = await ctx.db
			.query("kunden_leistungen")
			.withIndex("by_kunde", (q) => q.eq("kundenId", args.kundenId))
			.collect();

		const results = [];
		for (const leistung of leistungen) {
			const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
			const kontingent = await ctx.db.get(leistung.kontingentId);

			// Zählen, in wie vielen Lieferscheinen diese Leistung verwendet wird
			const lieferscheinZuordnungen = await ctx.db
				.query("kunden_lieferscheine_zuordnung")
				.withIndex("by_leistung", (q) => q.eq("leistungId", leistung._id))
				.collect();

			// Nur die neuesten Versionen von Lieferscheinen zählen
			const lieferscheinIds = new Set<string>();

			for (const zuordnung of lieferscheinZuordnungen) {
				const lieferschein = await ctx.db.get(zuordnung.lieferscheinId);
				if (!lieferschein) continue;

				// Wenn es ein Original ist, das korrigiert wurde, überspringen
				if (lieferschein.wurdeKorrigiert) continue;

				// Ansonsten hinzufügen (entweder Original ohne Korrektur oder neueste Korrektur)
				lieferscheinIds.add(lieferschein._id.toString());
			}

			results.push({
				...leistung,
				mitarbeiterName: mitarbeiter?.name || "Unbekannt",
				kontingentName: kontingent?.name || "Unbekannt",
				inLieferscheinen: lieferscheinIds.size,
			});
		}

		return results;
	},
});
