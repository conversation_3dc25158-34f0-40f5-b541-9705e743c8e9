import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { Doc, Id } from "../_generated/dataModel";

/**
 * Definition des Lieferschein-Objekts
 */
const lieferscheinObject = v.object({
	_id: v.id("kunden_lieferscheine"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	nummer: v.optional(v.string()),
	erstelltAm: v.number(),
	istKorrektur: v.boolean(),
	originalId: v.optional(v.id("kunden_lieferscheine")),
	korrekturVersion: v.optional(v.number()),
	bemerkung: v.optional(v.string()),
	status: v.string(), // "entwurf" oder "fertig"
	wurdeKorrigiert: v.optional(v.boolean()),
});

/**
 * Definition des erweiterten Lieferschein-Objekts mit Kundennamen
 */
const lieferscheinWithKundeObject = v.object({
	_id: v.id("kunden_lieferscheine"),
	_creationTime: v.number(),
	kundenId: v.id("kunden"),
	nummer: v.optional(v.string()),
	erstelltAm: v.number(),
	istKorrektur: v.boolean(),
	originalId: v.optional(v.id("kunden_lieferscheine")),
	korrekturVersion: v.optional(v.number()),
	bemerkung: v.optional(v.string()),
	status: v.string(), // "entwurf" oder "fertig"
	wurdeKorrigiert: v.optional(v.boolean()),
	kundeName: v.string(),
	erstelltAmFormatiert: v.string(),
	hatKorrektur: v.boolean(),
});

/**
 * Definition des Lieferschein-Leistung-Objekts
 */
const lieferscheinLeistungObject = v.object({
	_id: v.id("kunden_lieferscheine_zuordnung"),
	_creationTime: v.number(),
	lieferscheinId: v.id("kunden_lieferscheine"),
	leistungId: v.id("kunden_leistungen"),
});

/**
 * Neuen Lieferschein im Entwurfsstatus erstellen
 */
export const create = mutation({
	args: {
		kundenId: v.id("kunden"),
		leistungIds: v.optional(v.array(v.id("kunden_leistungen"))),
		bemerkung: v.optional(v.string()),
	},
	returns: v.id("kunden_lieferscheine"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Kunde prüfen
		const kunde = await ctx.db.get(args.kundenId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		// Lieferschein im Entwurfsstatus erstellen (ohne Nummer)
		const lieferscheinId = await ctx.db.insert("kunden_lieferscheine", {
			kundenId: args.kundenId,
			erstelltAm: Date.now(),
			istKorrektur: false,
			bemerkung: args.bemerkung,
			status: "entwurf", // Entwurfsstatus
			wurdeKorrigiert: false, // Initialisierung
		});

		// Leistungen hinzufügen, falls vorhanden
		if (args.leistungIds && args.leistungIds.length > 0) {
			for (const leistungId of args.leistungIds) {
				// Prüfen, ob die Leistung existiert und zum Kunden gehört
				const leistung = await ctx.db.get(leistungId);
				if (!leistung) {
					continue; // Leistung nicht gefunden, überspringen
				}
				if (leistung.kundenId !== args.kundenId) {
					continue; // Leistung gehört nicht zum Kunden, überspringen
				}

				// Leistung zum Lieferschein hinzufügen
				await ctx.db.insert("kunden_lieferscheine_zuordnung", {
					lieferscheinId,
					leistungId,
				});
			}
		}

		return lieferscheinId;
	},
});

/**
 * Lieferschein finalisieren und Nummer vergeben
 */
export const finalize = mutation({
	args: {
		lieferscheinId: v.id("kunden_lieferscheine"),
	},
	returns: v.id("kunden_lieferscheine"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein laden
		const lieferschein = await ctx.db.get(args.lieferscheinId);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Prüfen, ob der Lieferschein bereits finalisiert wurde
		if (lieferschein.status === "fertig" || lieferschein.nummer) {
			throw new Error("Lieferschein wurde bereits finalisiert.");
		}

		// Lieferscheinnummer generieren
		const currentYear = new Date().getFullYear();
		const yearSuffix = currentYear.toString().slice(-2); // Get only the last two digits of the year (YY)

		// Höchste Lieferscheinnummer für das aktuelle Jahr finden
		const lieferscheine = await ctx.db
			.query("kunden_lieferscheine")
			.filter((q) =>
				q.and(
					q.eq(q.field("istKorrektur"), false), // Nur Originale, keine Korrekturen
					q.neq(q.field("nummer"), undefined), // Nur Lieferscheine mit Nummer
					q.gte(q.field("nummer"), `${yearSuffix}00000`),
					q.lt(
						q.field("nummer"),
						`${(currentYear + 1).toString().slice(-2)}00000`,
					),
				),
			)
			.collect();

		// Höchste Nummer extrahieren
		let highestNumber = 0;
		for (const ls of lieferscheine) {
			if (ls.nummer) {
				const numberPart = parseInt(ls.nummer.substring(2), 10);
				if (numberPart > highestNumber) {
					highestNumber = numberPart;
				}
			}
		}

		// Nächste Nummer generieren
		const nextNumber = highestNumber + 1;

		// Lieferscheinnummer formatieren (YY + XXXXX)
		const nummer = `${yearSuffix}${nextNumber.toString().padStart(5, "0")}`;

		// Lieferschein aktualisieren
		await ctx.db.patch(args.lieferscheinId, {
			nummer,
			status: "fertig", // Status auf "fertig" setzen
		});

		return args.lieferscheinId;
	},
});

/**
 * Lieferschein-Korrektur erstellen
 */
export const createCorrection = mutation({
	args: {
		originalId: v.id("kunden_lieferscheine"),
		leistungIds: v.array(v.id("kunden_leistungen")),
		bemerkung: v.optional(v.string()),
	},
	returns: v.id("kunden_lieferscheine"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Original-Lieferschein laden
		const originalLieferschein = await ctx.db.get(args.originalId);
		if (!originalLieferschein) {
			throw new Error("Original-Lieferschein nicht gefunden.");
		}

		// Bestimmen des Basis-Originals und der Basis-Nummer
		let baseOriginalId = args.originalId;
		let baseOriginalNumber = originalLieferschein.nummer;

		if (originalLieferschein.istKorrektur && originalLieferschein.originalId) {
			// Wenn es eine Korrektur ist, verwenden wir das ursprüngliche Original als Basis
			baseOriginalId = originalLieferschein.originalId;

			// Original-Lieferschein laden
			const baseOriginal = await ctx.db.get(baseOriginalId);
			if (!baseOriginal) {
				throw new Error("Basis-Original-Lieferschein nicht gefunden.");
			}

			baseOriginalNumber = baseOriginal.nummer;

			// Bei Korrekturen von Korrekturen müssen wir nichts markieren,
			// da der Status "hatKorrektur" dynamisch berechnet wird
		}

		// Höchste Korrekturversion für diesen Lieferschein finden
		const korrekturen = await ctx.db
			.query("kunden_lieferscheine")
			.withIndex("by_original", (q) => q.eq("originalId", baseOriginalId))
			.collect();

		let highestVersion = 0;
		for (const korrektur of korrekturen) {
			if (
				korrektur.korrekturVersion &&
				korrektur.korrekturVersion > highestVersion
			) {
				highestVersion = korrektur.korrekturVersion;
			}
		}

		// Nächste Korrekturversion
		const nextVersion = highestVersion + 1;

		// Korrigierte Lieferscheinnummer
		const korrekturNummer = `${baseOriginalNumber}.${nextVersion}`;

		// Korrektur im Entwurfsstatus erstellen
		const korrekturId = await ctx.db.insert("kunden_lieferscheine", {
			kundenId: originalLieferschein.kundenId,
			erstelltAm: Date.now(),
			istKorrektur: true,
			originalId: baseOriginalId, // Immer auf das ursprüngliche Original verweisen
			korrekturVersion: nextVersion,
			bemerkung: args.bemerkung,
			status: "entwurf", // Entwurfsstatus
			wurdeKorrigiert: false, // Korrekturen können selbst nicht korrigiert werden
		});

		// Leistungen hinzufügen
		for (const leistungId of args.leistungIds) {
			// Prüfen, ob die Leistung existiert und zum Kunden gehört
			const leistung = await ctx.db.get(leistungId);
			if (!leistung) {
				continue; // Leistung nicht gefunden, überspringen
			}
			if (leistung.kundenId !== originalLieferschein.kundenId) {
				continue; // Leistung gehört nicht zum Kunden, überspringen
			}

			// Leistung zum Lieferschein hinzufügen
			await ctx.db.insert("kunden_lieferscheine_zuordnung", {
				lieferscheinId: korrekturId,
				leistungId,
			});
		}

		return korrekturId;
	},
});

/**
 * Korrektur finalisieren und Nummer vergeben
 */
export const finalizeCorrection = mutation({
	args: {
		korrekturId: v.id("kunden_lieferscheine"),
	},
	returns: v.id("kunden_lieferscheine"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Korrektur laden
		const korrektur = await ctx.db.get(args.korrekturId);
		if (!korrektur) {
			throw new Error("Korrektur nicht gefunden.");
		}

		// Prüfen, ob es sich um eine Korrektur handelt
		if (!korrektur.istKorrektur) {
			throw new Error("Dies ist keine Korrektur.");
		}

		// Prüfen, ob eine originalId vorhanden ist
		if (!korrektur.originalId) {
			throw new Error("Keine Original-ID für die Korrektur gefunden.");
		}

		// Prüfen, ob die Korrektur bereits finalisiert wurde
		if (korrektur.status === "fertig" || korrektur.nummer) {
			throw new Error("Korrektur wurde bereits finalisiert.");
		}

		// Original-Lieferschein laden
		// Die Prüfung auf originalId wurde bereits oben durchgeführt
		const original = await ctx.db.get(korrektur.originalId);
		if (!original) {
			throw new Error("Original-Lieferschein nicht gefunden.");
		}

		// Prüfen, ob das Original eine Nummer hat
		if (!original.nummer) {
			throw new Error("Das Original hat keine Nummer.");
		}

		// Korrekturversion prüfen und anpassen
		let korrekturVersion = korrektur.korrekturVersion || 1;

		// Wenn es die erste Korrektur ist, starten wir mit Version 2
		if (korrekturVersion === 1) {
			korrekturVersion = 2;
		}

		// Korrektur-Nummer generieren (Format: YYXXXXX.Z)
		const korrekturNummer = `${original.nummer}.${korrekturVersion}`;

		// Original als korrigiert markieren
		await ctx.db.patch(korrektur.originalId, {
			wurdeKorrigiert: true,
		});

		// Korrektur aktualisieren
		await ctx.db.patch(args.korrekturId, {
			nummer: korrekturNummer,
			korrekturVersion: korrekturVersion,
			status: "fertig", // Status auf "fertig" setzen
		});

		return args.korrekturId;
	},
});

/**
 * Leistung zu einem Lieferschein hinzufügen
 */
export const addLeistung = mutation({
	args: {
		lieferscheinId: v.id("kunden_lieferscheine"),
		leistungId: v.id("kunden_leistungen"),
	},
	returns: v.id("kunden_lieferscheine_zuordnung"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein und Leistung laden
		const lieferschein = await ctx.db.get(args.lieferscheinId);
		const leistung = await ctx.db.get(args.leistungId);

		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}
		if (!leistung) {
			throw new Error("Leistung nicht gefunden.");
		}

		// Prüfen, ob der Lieferschein im Entwurfsstatus ist
		if (lieferschein.status === "fertig") {
			throw new Error(
				"Der Lieferschein ist bereits finalisiert und kann nicht mehr bearbeitet werden.",
			);
		}

		// Prüfen, ob die Leistung zum selben Kunden gehört
		if (leistung.kundenId !== lieferschein.kundenId) {
			throw new Error(
				"Die Leistung gehört nicht zum selben Kunden wie der Lieferschein.",
			);
		}

		// Prüfen, ob die Leistung bereits auf diesem Lieferschein ist
		const existing = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein_and_leistung", (q) =>
				q
					.eq("lieferscheinId", args.lieferscheinId)
					.eq("leistungId", args.leistungId),
			)
			.first();

		if (existing) {
			throw new Error("Diese Leistung ist bereits auf diesem Lieferschein.");
		}

		// Leistung zum Lieferschein hinzufügen
		return await ctx.db.insert("kunden_lieferscheine_zuordnung", {
			lieferscheinId: args.lieferscheinId,
			leistungId: args.leistungId,
		});
	},
});

/**
 * Leistung von einem Lieferschein entfernen
 */
export const removeLeistung = mutation({
	args: {
		lieferscheinId: v.id("kunden_lieferscheine"),
		leistungId: v.id("kunden_leistungen"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein laden
		const lieferschein = await ctx.db.get(args.lieferscheinId);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Prüfen, ob der Lieferschein im Entwurfsstatus ist
		if (lieferschein.status === "fertig") {
			throw new Error(
				"Der Lieferschein ist bereits finalisiert und kann nicht mehr bearbeitet werden.",
			);
		}

		// Verknüpfung finden
		const verknuepfung = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein_and_leistung", (q) =>
				q
					.eq("lieferscheinId", args.lieferscheinId)
					.eq("leistungId", args.leistungId),
			)
			.first();

		if (!verknuepfung) {
			throw new Error("Diese Leistung ist nicht auf diesem Lieferschein.");
		}

		// Verknüpfung löschen
		await ctx.db.delete(verknuepfung._id);
		return null;
	},
});

/**
 * Definition des Lieferschein-Gruppe-Objekts für die hierarchische Darstellung
 */
const lieferscheinGruppeObject = v.object({
	hauptLieferschein: lieferscheinWithKundeObject,
	korrekturen: v.array(lieferscheinWithKundeObject),
	original: v.union(lieferscheinWithKundeObject, v.null()),
});

/**
 * Alle Lieferscheine mit Kundennamen abrufen
 */
export const list = query({
	args: {},
	returns: v.array(lieferscheinGruppeObject),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const lieferscheine = await ctx.db.query("kunden_lieferscheine").collect();

		// Sammle alle Lieferscheine und ihre Korrekturen
		const lieferscheinMap = new Map();
		const korrekturMap = new Map();

		// Zuerst alle Lieferscheine in Maps organisieren
		for (const lieferschein of lieferscheine) {
			if (lieferschein.istKorrektur && lieferschein.originalId) {
				// Korrekturen nach Original gruppieren
				if (!korrekturMap.has(lieferschein.originalId.toString())) {
					korrekturMap.set(lieferschein.originalId.toString(), []);
				}
				korrekturMap.get(lieferschein.originalId.toString()).push(lieferschein);
			} else {
				// Originale in eigene Map
				lieferscheinMap.set(lieferschein._id.toString(), lieferschein);
			}
		}

		const results = [];

		// Verarbeite alle Lieferscheine
		for (const [id, lieferschein] of lieferscheinMap.entries()) {
			const kunde = (await ctx.db.get(
				lieferschein.kundenId,
			)) as Doc<"kunden"> | null;

			// Prüfen, ob es Korrekturen für diesen Lieferschein gibt
			const korrekturen = korrekturMap.get(id) || [];
			const hatKorrektur = korrekturen.length > 0;

			// Lieferschein mit Kundennamen
			const lieferscheinWithKunde = {
				...lieferschein,
				kundeName: kunde?.name || "Unbekannt",
				erstelltAmFormatiert: new Date(
					lieferschein.erstelltAm,
				).toLocaleDateString("de-DE"),
				hatKorrektur,
			};

			// Korrekturen mit Kundennamen anreichern
			const korrekturenWithKunde = korrekturen.map(
				(korrektur: Doc<"kunden_lieferscheine">) => ({
					...korrektur,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						korrektur.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: false, // Korrekturen können selbst keine Korrekturen haben
				}),
			);

			// Nach Version sortieren (absteigend)
			korrekturenWithKunde.sort(
				(a: any, b: any) =>
					(b.korrekturVersion || 0) - (a.korrekturVersion || 0),
			);

			if (hatKorrektur) {
				// Wenn es Korrekturen gibt, nehmen wir die neueste als Hauptlieferschein
				const neuesteKorrektur = korrekturenWithKunde[0];

				// Entferne die neueste Korrektur aus der Liste der anderen Korrekturen
				const andereKorrekturen = korrekturenWithKunde.slice(1);

				results.push({
					hauptLieferschein: neuesteKorrektur,
					korrekturen: andereKorrekturen,
					original: lieferscheinWithKunde,
				});
			} else {
				// Wenn es keine Korrekturen gibt, ist das Original der Hauptlieferschein
				results.push({
					hauptLieferschein: lieferscheinWithKunde,
					korrekturen: [],
					original: null,
				});
			}
		}

		// Nach Erstellungsdatum sortieren (neueste zuerst)
		return results.sort(
			(a: any, b: any) =>
				b.hauptLieferschein.erstelltAm - a.hauptLieferschein.erstelltAm,
		);
	},
});

/**
 * Lieferschein mit Details abrufen
 */
export const get = query({
	args: {
		id: v.id("kunden_lieferscheine"),
	},
	returns: v.object({
		lieferschein: lieferscheinWithKundeObject,
		leistungen: v.array(
			v.object({
				_id: v.id("kunden_leistungen"),
				_creationTime: v.number(),
				startZeit: v.number(),
				endZeit: v.number(),
				art: v.string(),
				beschreibung: v.string(),
				stunden: v.number(),
				stundenpreis: v.number(),
				anfahrtskosten: v.number(),
				mitAnfahrt: v.boolean(),
				datum: v.string(),
				startZeitFormatiert: v.string(),
				endZeitFormatiert: v.string(),
				kundenId: v.id("kunden"),
				mitarbeiterId: v.id("mitarbeiter"),
				kontingentId: v.id("kunden_kontingente"),
				kontingentName: v.optional(v.string()),
				mitarbeiterName: v.optional(v.string()),
			}),
		),
		korrekturen: v.array(lieferscheinWithKundeObject),
		original: v.union(lieferscheinWithKundeObject, v.null()),
	}),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein laden
		const lieferschein = await ctx.db.get(args.id);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Kunde laden
		const kunde = await ctx.db.get(lieferschein.kundenId);

		// Prüfen, ob es Korrekturen für diesen Lieferschein gibt
		const hatKorrektur = lieferschein.istKorrektur
			? false // Korrekturen können selbst keine Korrekturen haben
			: (await ctx.db
						.query("kunden_lieferscheine")
						.withIndex("by_original", (q) =>
							q.eq("originalId", lieferschein._id),
						)
						.first())
				? true
				: false;

		// Lieferschein mit Kundennamen
		const lieferscheinWithKunde = {
			...lieferschein,
			kundeName: kunde?.name || "Unbekannt",
			erstelltAmFormatiert: new Date(
				lieferschein.erstelltAm,
			).toLocaleDateString("de-DE"),
			hatKorrektur,
		};

		// Leistungen für diesen Lieferschein laden
		const lieferscheinLeistungen = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein", (q) => q.eq("lieferscheinId", args.id))
			.collect();

		// Leistungsdetails laden
		const leistungen = [];
		for (const ll of lieferscheinLeistungen) {
			const leistung = await ctx.db.get(ll.leistungId);
			if (leistung) {
				// Kontingent und Mitarbeiter laden
				let kontingentName = undefined;
				let mitarbeiterName = undefined;

				if (leistung.kontingentId) {
					const kontingent = await ctx.db.get(leistung.kontingentId);
					if (kontingent) {
						kontingentName = kontingent.name;
					}
				}

				if (leistung.mitarbeiterId) {
					const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
					if (mitarbeiter) {
						mitarbeiterName = mitarbeiter.name;
					}
				}

				leistungen.push({
					...leistung,
					datum: new Date(leistung.startZeit).toLocaleDateString("de-DE"),
					startZeitFormatiert: new Date(leistung.startZeit).toLocaleTimeString(
						"de-DE",
						{
							hour: "2-digit",
							minute: "2-digit",
						},
					),
					endZeitFormatiert: new Date(leistung.endZeit).toLocaleTimeString(
						"de-DE",
						{
							hour: "2-digit",
							minute: "2-digit",
						},
					),
					kontingentName,
					mitarbeiterName,
				});
			}
		}

		// Nach Datum sortieren
		leistungen.sort((a: any, b: any) => a.startZeit - b.startZeit);

		// Korrekturen laden, falls es ein Original ist
		let korrekturen = [];
		if (!lieferschein.istKorrektur) {
			const korrekturenDocs = await ctx.db
				.query("kunden_lieferscheine")
				.withIndex("by_original", (q) => q.eq("originalId", args.id))
				.collect();

			for (const korrektur of korrekturenDocs) {
				korrekturen.push({
					...korrektur,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						korrektur.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: false,
				});
			}

			// Nach Version sortieren
			korrekturen.sort(
				(a, b) => (b.korrekturVersion || 0) - (a.korrekturVersion || 0),
			);
		}

		// Original laden, falls es eine Korrektur ist
		let original = null;
		if (lieferschein.istKorrektur && lieferschein.originalId) {
			const originalDoc = await ctx.db.get(lieferschein.originalId);
			if (originalDoc) {
				// Prüfen, ob das Original weitere Korrekturen hat
				const hatWeitereKorrekturen = (await ctx.db
					.query("kunden_lieferscheine")
					.withIndex("by_original", (q) => q.eq("originalId", originalDoc._id))
					.filter((q) => q.neq(q.field("_id"), args.id)) // Ausschließen der aktuellen Korrektur
					.first())
					? true
					: false;

				original = {
					...originalDoc,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						originalDoc.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: hatWeitereKorrekturen,
				};
			}
		}

		return {
			lieferschein: lieferscheinWithKunde,
			leistungen,
			korrekturen,
			original,
		};
	},
});

/**
 * Lieferschein aktualisieren
 */
export const update = mutation({
	args: {
		id: v.id("kunden_lieferscheine"),
		bemerkung: v.optional(v.string()),
	},
	returns: v.id("kunden_lieferscheine"),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein laden
		const lieferschein = await ctx.db.get(args.id);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Prüfen, ob der Lieferschein im Entwurfsstatus ist
		if (lieferschein.status === "fertig") {
			throw new Error(
				"Finalisierte Lieferscheine können nicht bearbeitet werden.",
			);
		}

		// Lieferschein aktualisieren
		await ctx.db.patch(args.id, {
			bemerkung: args.bemerkung,
		});

		return args.id;
	},
});

/**
 * Lieferschein anhand der ID abrufen (nur Lieferschein-Daten)
 */
export const getById = query({
	args: {
		id: v.id("kunden_lieferscheine"),
	},
	returns: lieferscheinObject,
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein abrufen
		const lieferschein = await ctx.db.get(args.id);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		return lieferschein;
	},
});

/**
 * Lieferschein löschen (nur Entwürfe und Korrekturen im Entwurfsstatus)
 */
export const remove = mutation({
	args: {
		id: v.id("kunden_lieferscheine"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein laden
		const lieferschein = await ctx.db.get(args.id);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Prüfen, ob der Lieferschein im Entwurfsstatus ist
		if (lieferschein.status !== "entwurf") {
			throw new Error(
				"Nur Lieferscheine im Entwurfsstatus können gelöscht werden.",
			);
		}

		// Alle Leistungszuordnungen für diesen Lieferschein löschen
		const zuordnungen = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein", (q) => q.eq("lieferscheinId", args.id))
			.collect();

		for (const zuordnung of zuordnungen) {
			await ctx.db.delete(zuordnung._id);
		}

		// Lieferschein löschen
		await ctx.db.delete(args.id);
		return null;
	},
});

/**
 * Lieferschein mit Details anhand der Nummer abrufen
 */
export const getByNummer = query({
	args: {
		nummer: v.string(),
	},
	returns: v.object({
		lieferschein: lieferscheinWithKundeObject,
		leistungen: v.array(
			v.object({
				_id: v.id("kunden_leistungen"),
				_creationTime: v.number(),
				startZeit: v.number(),
				endZeit: v.number(),
				art: v.string(),
				beschreibung: v.string(),
				stunden: v.number(),
				stundenpreis: v.number(),
				anfahrtskosten: v.number(),
				mitAnfahrt: v.boolean(),
				datum: v.string(),
				startZeitFormatiert: v.string(),
				endZeitFormatiert: v.string(),
				kundenId: v.id("kunden"),
				mitarbeiterId: v.id("mitarbeiter"),
				kontingentId: v.id("kunden_kontingente"),
				kontingentName: v.optional(v.string()),
				mitarbeiterName: v.optional(v.string()),
			}),
		),
		korrekturen: v.array(lieferscheinWithKundeObject),
		original: v.union(lieferscheinWithKundeObject, v.null()),
	}),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Lieferschein anhand der Nummer suchen
		const lieferschein = await ctx.db
			.query("kunden_lieferscheine")
			.withIndex("by_nummer", (q) => q.eq("nummer", args.nummer))
			.first();

		// Wenn keine Nummer angegeben wurde oder der Lieferschein nicht gefunden wurde
		if (!args.nummer || !lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Kunde laden
		const kunde = await ctx.db.get(lieferschein.kundenId);

		// Prüfen, ob es Korrekturen für diesen Lieferschein gibt
		const hatKorrektur = lieferschein.istKorrektur
			? false // Korrekturen können selbst keine Korrekturen haben
			: (await ctx.db
						.query("kunden_lieferscheine")
						.withIndex("by_original", (q) =>
							q.eq("originalId", lieferschein._id),
						)
						.first())
				? true
				: false;

		// Lieferschein mit Kundennamen
		const lieferscheinWithKunde = {
			...lieferschein,
			kundeName: kunde?.name || "Unbekannt",
			erstelltAmFormatiert: new Date(
				lieferschein.erstelltAm,
			).toLocaleDateString("de-DE"),
			hatKorrektur,
		};

		// Leistungen für diesen Lieferschein laden
		const lieferscheinLeistungen = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein", (q) =>
				q.eq("lieferscheinId", lieferschein._id),
			)
			.collect();

		// Leistungsdetails laden
		const leistungen = [];
		for (const ll of lieferscheinLeistungen) {
			const leistung = await ctx.db.get(ll.leistungId);
			if (leistung) {
				// Kontingent und Mitarbeiter laden
				let kontingentName = undefined;
				let mitarbeiterName = undefined;

				if (leistung.kontingentId) {
					const kontingent = await ctx.db.get(leistung.kontingentId);
					if (kontingent) {
						kontingentName = kontingent.name;
					}
				}

				if (leistung.mitarbeiterId) {
					const mitarbeiter = await ctx.db.get(leistung.mitarbeiterId);
					if (mitarbeiter) {
						mitarbeiterName = mitarbeiter.name;
					}
				}

				leistungen.push({
					...leistung,
					datum: new Date(leistung.startZeit).toLocaleDateString("de-DE"),
					startZeitFormatiert: new Date(leistung.startZeit).toLocaleTimeString(
						"de-DE",
						{
							hour: "2-digit",
							minute: "2-digit",
						},
					),
					endZeitFormatiert: new Date(leistung.endZeit).toLocaleTimeString(
						"de-DE",
						{
							hour: "2-digit",
							minute: "2-digit",
						},
					),
					kontingentName,
					mitarbeiterName,
				});
			}
		}

		// Nach Datum sortieren
		leistungen.sort((a: any, b: any) => a.startZeit - b.startZeit);

		// Korrekturen laden, falls es ein Original ist
		let korrekturen = [];
		if (!lieferschein.istKorrektur) {
			const korrekturenDocs = await ctx.db
				.query("kunden_lieferscheine")
				.withIndex("by_original", (q) => q.eq("originalId", lieferschein._id))
				.collect();

			for (const korrektur of korrekturenDocs) {
				korrekturen.push({
					...korrektur,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						korrektur.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: false,
				});
			}

			// Nach Version sortieren
			korrekturen.sort(
				(a: Doc<"kunden_lieferscheine">, b: Doc<"kunden_lieferscheine">) =>
					(b.korrekturVersion || 0) - (a.korrekturVersion || 0),
			);
		}

		// Original laden, falls es eine Korrektur ist
		let original = null;
		if (lieferschein.istKorrektur && lieferschein.originalId) {
			const originalDoc = await ctx.db.get(lieferschein.originalId);
			if (originalDoc) {
				// Prüfen, ob das Original weitere Korrekturen hat
				const hatWeitereKorrekturen = (await ctx.db
					.query("kunden_lieferscheine")
					.withIndex("by_original", (q) => q.eq("originalId", originalDoc._id))
					.filter((q) => q.neq(q.field("_id"), lieferschein._id)) // Ausschließen der aktuellen Korrektur
					.first())
					? true
					: false;

				original = {
					...originalDoc,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						originalDoc.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: hatWeitereKorrekturen,
				};
			}
		}

		return {
			lieferschein: lieferscheinWithKunde,
			leistungen,
			korrekturen,
			original,
		};
	},
});
