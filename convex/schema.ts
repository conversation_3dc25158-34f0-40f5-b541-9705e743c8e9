import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Field types for documentation categories
export const FIELD_TYPES = {
	TEXT: "text",
	TEXTAREA: "textarea",
	NUMBER: "number",
	PASSWORD: "password",
	URL: "url",
	EMAIL: "email",
	DATE: "date",
	CHECKBOX: "checkbox",
	SELECT: "select",
	PHONE: "phone",
};

export default defineSchema({
	kunden: defineTable({
		name: v.string(),
		stundenpreis: v.number(),
		anfahrtskosten: v.number(),
		standorte: v.array(
			v.object({
				strasse: v.string(),
				plz: v.string(),
				ort: v.string(),
				land: v.optional(v.string()),
				istHauptstandort: v.boolean(),
			}),
		),
		ansprechpartner: v.array(
			v.object({
				name: v.string(),
				email: v.optional(v.string()),
				telefon: v.optional(v.string()),
				mobil: v.optional(v.string()),
				position: v.optional(v.string()),
				istHauptansprechpartner: v.boolean(),
				istEmailLieferscheinEmpfaenger: v.optional(v.boolean()),
				istEmailUebersichtEmpfaenger: v.optional(v.boolean()),
				istEmailAnrede: v.optional(v.boolean()),
			}),
		),
	}),
	mitarbeiter: defineTable({
		name: v.string(),
		email: v.string(),
	}),
	kunden_kontingente: defineTable({
		kundenId: v.id("kunden"),
		name: v.string(),
		stunden: v.number(),
		verbrauchteStunden: v.number(),
		startDatum: v.number(),
		endDatum: v.number(),
		istAktiv: v.boolean(),
	})
		.index("by_kunde_and_status", ["kundenId", "istAktiv"])
		.index("by_status", ["istAktiv"]),
	kunden_leistungen: defineTable({
		kundenId: v.id("kunden"),
		mitarbeiterId: v.id("mitarbeiter"),
		kontingentId: v.id("kunden_kontingente"),
		startZeit: v.number(),
		endZeit: v.number(),
		art: v.string(),
		mitAnfahrt: v.boolean(),
		beschreibung: v.string(),
		stunden: v.number(),
		stundenpreis: v.number(),
		anfahrtskosten: v.number(),
	})
		.index("by_kunde", ["kundenId"])
		.index("by_mitarbeiter", ["mitarbeiterId"])
		.index("by_kontingent", ["kontingentId"]),
	system_feedback: defineTable({
		userName: v.string(),
		text: v.string(),
		type: v.string(),
		status: v.string(),
	})
		.index("by_status", ["status"])
		.index("by_type", ["type"])
		.index("by_status_and_type", ["status", "type"]),
	system_doku_kategorien: defineTable({
		name: v.string(),
		beschreibung: v.string(),
		kategorieID: v.number(),
		felder: v.array(
			v.object({
				feldId: v.number(),
				name: v.string(),
				typ: v.string(),
				istErforderlich: v.boolean(),
				optionen: v.optional(v.array(v.string())),
				placeholder: v.optional(v.string()),
				istKopierbar: v.optional(v.boolean()),
				istVersteckt: v.optional(v.boolean()),
				istExtrafeld: v.optional(v.boolean()),
			}),
		),
		reihenfolge: v.number(),
	}).index("by_name", ["name"]),
	kunden_dokumentation: defineTable({
		kundenId: v.id("kunden"),
		kategorieID: v.number(),
		feldwerte: v.array(
			v.object({
				feldId: v.number(),
				feldWert: v.string(),
			}),
		),
	})
		.index("by_kunde", ["kundenId"])
		.index("by_kunde_and_kategorie", ["kundenId", "kategorieID"]),
	kunden_lieferscheine: defineTable({
		kundenId: v.id("kunden"),
		nummer: v.optional(v.string()),
		erstelltAm: v.number(),
		istKorrektur: v.boolean(),
		originalId: v.optional(v.id("kunden_lieferscheine")),
		korrekturVersion: v.optional(v.number()),
		bemerkung: v.optional(v.string()),
		status: v.string(),
		wurdeKorrigiert: v.optional(v.boolean()),
	})
		.index("by_kunde", ["kundenId"])
		.index("by_nummer", ["nummer"])
		.index("by_original", ["originalId"]),
	kunden_lieferscheine_zuordnung: defineTable({
		lieferscheinId: v.id("kunden_lieferscheine"),
		leistungId: v.id("kunden_leistungen"),
	})
		.index("by_lieferschein", ["lieferscheinId"])
		.index("by_leistung", ["leistungId"])
		.index("by_lieferschein_and_leistung", ["lieferscheinId", "leistungId"]),
});
