import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { Doc } from "../_generated/dataModel";
import { DOKU_KATEGORIEN } from "./dokuKategorienConfig";

// Define the structure for a documentation category field
const feldSchema = v.object({
	feldId: v.number(),
	name: v.string(),
	typ: v.string(),
	istErforderlich: v.boolean(),
	optionen: v.optional(v.array(v.string())),
	placeholder: v.optional(v.string()),
	istKopierbar: v.optional(v.boolean()),
	istVersteckt: v.optional(v.boolean()),
	istExtrafeld: v.optional(v.boolean()),
});

// Define the structure for a documentation category
const kategorieSchema = v.object({
	_id: v.id("system_doku_kategorien"),
	_creationTime: v.number(),
	name: v.string(),
	beschreibung: v.string(),
	kategorieID: v.optional(v.number()),
	felder: v.array(feldSchema),
	reihenfolge: v.number(),
});

/**
 * Liste aller Dokumentationskategorien zurückgeben
 */
export const list = query({
	args: {},
	returns: v.array(kategorieSchema),
	handler: async (ctx) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Alle Kategorien abrufen und nach Reihenfolge sortieren
		const kategorien = await ctx.db.query("system_doku_kategorien").collect();
		return kategorien.sort((a, b) => a.reihenfolge - b.reihenfolge);
	},
});

/**
 * Einzelne Dokumentationskategorie anhand ihrer ID abrufen
 */
export const get = query({
	args: { id: v.id("system_doku_kategorien") },
	returns: v.union(kategorieSchema, v.null()),
	handler: async (ctx, args) => {
		// Authentifizierung prüfen
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.get(args.id);
	},
});

/**
 * Synchronisiert vordefinierte Dokumentationskategorien.
 * - Fügt neue hinzu.
 * - Aktualisiert bestehende vordefinierte Kategorien (Name, Beschreibung, Felder, Reihenfolge).
 * - Entfernt vordefinierte Kategorien, die nicht mehr in PREDEFINED_CATEGORIES existieren,
 *   aber NUR, wenn keine Dokumentationseinträge damit verknüpft sind.
 */
export const initializeDefaults = mutation({
	args: {},
	returns: v.string(),
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity && !process.env.CONVEX_CLOUD_URL) {
			console.warn(
				"Synchronisiere Standardkategorien ohne Benutzeridentität (nur für Entwicklung/Systemstart).",
			);
		} else if (!identity && process.env.CONVEX_CLOUD_URL) {
			throw new Error("Nicht authentifiziert für Synchronisation.");
		}

		const dbCategories = await ctx.db.query("system_doku_kategorien").collect();
		const predefinedCategoryNames = new Set(
			DOKU_KATEGORIEN.map((pc) => pc.name),
		);

		let addedCount = 0;
		let updatedCount = 0;
		let removedCount = 0;
		let notRemovedDueToUsageCount = 0;

		// 1. Add or Update predefined categories from the DOKU_KATEGORIEN list
		for (const predefinedCat of DOKU_KATEGORIEN) {
			const existingDbCat = dbCategories.find(
				(dbCat) => dbCat.name === predefinedCat.name,
			);

			// Ensure feldId is present, unique, and include all properties
			const processedFelder = predefinedCat.felder.map((feld) => {
				const {
					feldId,
					name,
					typ,
					istErforderlich,
					placeholder,
					istKopierbar,
					istVersteckt,
					istExtrafeld,
				} = feld;
				// Explicitly check for optionen and include if present
				const optionen = feld.optionen || undefined;
				return {
					feldId,
					name,
					typ,
					istErforderlich,
					optionen: optionen,
					placeholder: placeholder || undefined,
					istKopierbar: istKopierbar || undefined,
					istVersteckt: istVersteckt || undefined,
					istExtrafeld: istExtrafeld || undefined,
				};
			});

			if (existingDbCat) {
				// Update existing category
				const updates: Partial<Doc<"system_doku_kategorien">> = {};
				if (existingDbCat.beschreibung !== predefinedCat.beschreibung)
					updates.beschreibung = predefinedCat.beschreibung;
				if (existingDbCat.reihenfolge !== predefinedCat.reihenfolge)
					updates.reihenfolge = predefinedCat.reihenfolge;
				// Update kategorieID if it's different or missing
				if (existingDbCat.kategorieID !== predefinedCat.kategorieID) {
					updates.kategorieID = predefinedCat.kategorieID;
				}
				// Deep compare fields for changes (simplistic check for now, can be more robust)
				if (
					JSON.stringify(existingDbCat.felder) !==
					JSON.stringify(processedFelder)
				) {
					updates.felder = processedFelder;
				}
				if (Object.keys(updates).length > 0) {
					await ctx.db.patch(existingDbCat._id, updates);
					updatedCount++;
				}
			} else {
				// Add new category
				await ctx.db.insert("system_doku_kategorien", {
					...predefinedCat,
					felder: processedFelder,
				});
				addedCount++;
			}
		}

		// 2. Remove categories from DB that are no longer in the config file
		const categoriesToRemove = dbCategories.filter(
			(dbCat) => !predefinedCategoryNames.has(dbCat.name),
		);

		for (const categoryToRemove of categoriesToRemove) {
			// Check if any entries are using this category
			const linkedEntries = await ctx.db
				.query("kunden_dokumentation")
				.filter((q) =>
					q.eq(q.field("kategorieID"), categoryToRemove.kategorieID),
				)
				.first();

			if (linkedEntries) {
				notRemovedDueToUsageCount++;
				console.warn(
					`Kategorie "${categoryToRemove.name}" (ID: ${categoryToRemove.kategorieID}) wird verwendet und kann nicht entfernt werden.`,
				);
			} else {
				await ctx.db.delete(categoryToRemove._id);
				removedCount++;
			}
		}

		let message = "Synchronisation abgeschlossen.";
		if (addedCount > 0) message += ` ${addedCount} neu hinzugefügt.`;
		if (updatedCount > 0) message += ` ${updatedCount} aktualisiert.`;
		if (removedCount > 0) message += ` ${removedCount} entfernt.`;
		if (notRemovedDueToUsageCount > 0)
			message += ` ${notRemovedDueToUsageCount} konnten nicht entfernt werden (in Benutzung).`;
		if (
			addedCount === 0 &&
			updatedCount === 0 &&
			removedCount === 0 &&
			notRemovedDueToUsageCount === 0
		) {
			message = "Keine Änderungen an Standardkategorien erforderlich.";
		}
		return message;
	},
});
