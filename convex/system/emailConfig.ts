/**
 * Configuration file for email system
 * This file contains settings for the TurboSMTP email service
 */

// TurboSMTP configuration
export interface EmailConfig {
	consumerKey: string;
	consumerSecret: string;
	sender: {
		email: string;
		name: string;
	};
	endpoint: string;
}

// Email templates interface
export interface EmailTemplates {
	lieferschein: {
		subject: string;
		body: string;
	};
	uebersicht: {
		subject: string;
		body: string;
	};
}

// Default email configuration
export const defaultEmailConfig: EmailConfig = {
	consumerKey: "",
	consumerSecret: "",
	sender: {
		email: "",
		name: "",
	},
	endpoint: "",
};

// Default email templates
export const defaultEmailTemplates: EmailTemplates = {
	lieferschein: {
		subject: "Lieferschein: {{nummer}}",
		body: `Sehr geehrte(r) {{empfaenger}},

anbei erhalten Sie den Lieferschein {{nummer}} vom {{datum}}.

Mit freundlichen Grüßen,
Ihre innov8-IT`,
	},
	uebersicht: {
		subject: "Übersicht: {{kunde}} - {{zeitraum}}",
		body: `Sehr geehrte(r) {{empfaenger}},

anbei erhalten Sie die Übersicht für {{kunde}} für den Zeitraum {{zeitraum}}.

Mit freundlichen Grüßen,
Ihre innov8-IT`,
	},
};

// Function to get email configuration with environment variables
export function getEmailConfig(): EmailConfig {
	return {
		consumerKey: process.env.TURBOSMTP_CONSUMER_KEY || "",
		consumerSecret: process.env.TURBOSMTP_CONSUMER_SECRET || "",
		sender: {
			email: process.env.EMAIL_FROM || "",
			name: process.env.EMAIL_FROM_NAME || "",
		},
		endpoint: process.env.TURBOSMTP_API_URL || "",
	};
}

// Function to get email templates
export function getEmailTemplates(): EmailTemplates {
	return defaultEmailTemplates;
}
