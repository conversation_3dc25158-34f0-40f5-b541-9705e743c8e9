import { v } from "convex/values";
import { query } from "../_generated/server";
import { defaultSettings } from "./standardsConfig";

/**
 * Get settings by type from the standardsConfig file
 */
export const getByType = query({
	args: {
		typ: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return the settings from the config file based on type
		if (args.typ === "uebersicht") {
			return defaultSettings.uebersicht;
		} else if (args.typ === "lieferschein") {
			return defaultSettings.lieferschein;
		} else {
			throw new Error(`Unbekannter Einstellungstyp: ${args.typ}`);
		}
	},
});

/**
 * Get specific settings by type
 */
export const getSettings = query({
	args: {
		type: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return the specific settings type
		if (args.type === "email") {
			return defaultSettings.email;
		} else if (args.type === "uebersicht") {
			return defaultSettings.uebersicht;
		} else if (args.type === "lieferschein") {
			return defaultSettings.lieferschein;
		} else {
			throw new Error(`Unbekannter Einstellungstyp: ${args.type}`);
		}
	},
});

/**
 * Get all settings from the standardsConfig file
 */
export const getAll = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return all settings from the config file
		return defaultSettings;
	},
});
