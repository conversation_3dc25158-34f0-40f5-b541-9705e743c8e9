/**
 * Configuration file for system standards
 * This file contains all the default settings for different document types
 */

// Common settings interface
interface CommonSettings {
	// Display options
	includeHeader: boolean;
	showLogo: boolean;
	includeFooter: boolean;

	// Content
	fusszeileText: string;
	logoPath: string;
}

// Uebersicht specific settings
interface UebersichtSettings extends CommonSettings {
	// Display options
	includeLeistungsuebersicht: boolean;
	includeKontingentuebersicht: boolean;
	includeSummary: boolean;
}

// Lieferschein specific settings
interface LieferscheinSettings extends CommonSettings {
	// Display options
	includeSignatureField: boolean;
	// Content
	legalText: string;
	signatureText: string;
}

// Email specific settings
interface EmailSettings {
	// Display options
	defaultSendToMitarbeiter: boolean;
	defaultSendToKunde: boolean;
}

// All system settings
export interface SystemSettings {
	uebersicht: UebersichtSettings;
	lieferschein: LieferscheinSettings;
	email: EmailSettings;
}

// Default system settings
export const defaultSettings: SystemSettings = {
	uebersicht: {
		// Display options
		includeHeader: true,
		showLogo: true,
		includeFooter: true,
		includeLeistungsuebersicht: true,
		includeKontingentuebersicht: true,
		includeSummary: true,

		// Content
		fusszeileText: "© innov8-IT",
		logoPath: "/logos/logo.png",
	},
	lieferschein: {
		// Display options
		includeHeader: true,
		showLogo: true,
		includeFooter: true,
		includeSignatureField: true,

		// Content
		legalText:
			"Grundlage der Geschäftsbeziehung bilden die Allgemeinen Geschäftsbedingungen (AGB), der IT-Dienstleistungsvertrag (Rahmenvertrag), die Auftragsverarbeitungsvereinbarung (AVV) sowie die Datenschutzerklärung der innov8-IT, jeweils in ihrer aktuell gültigen Fassung.\n\nAlle Preise sind Nettopreise und verstehen sich zuzüglich der gesetzlichen Mehrwertsteuer in Höhe von derzeit 19%.\n\nLeistungen und Waren gelten als abgenommen und genehmigt, sofern innerhalb von 7 Tagen nach Erhalt des Lieferscheins weder eine Unterschrift des Auftraggebers erfolgt noch ein schriftlicher Widerspruch oder eine Mängelrüge eingereicht wird.",
		signatureText: "Ort / Datum / Unterschrift / Stempel",
		fusszeileText: "© innov8-IT",
		logoPath: "/logos/logo.png",
	},
	email: {
		// Display options
		defaultSendToMitarbeiter: true,
		defaultSendToKunde: false,
	},
};

// Function to get settings for a specific type
export function getSettings<T extends keyof SystemSettings>(
	type: T,
): SystemSettings[T] {
	return defaultSettings[type];
}
