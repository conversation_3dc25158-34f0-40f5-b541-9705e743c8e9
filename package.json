{"name": "8it-verwal<PERSON>g", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node ./scripts/load-env.js .env.local 'vite'", "build": "node ./scripts/load-env.js .env.production 'tsc && vite build'", "lint": "biome lint ./src ./convex", "format": "biome format --write ./src ./convex", "check": "biome check --apply ./src ./convex", "start": "node ./scripts/load-env.js .env.production 'vite preview'"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@clerk/clerk-react": "^5.30.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@react-pdf/renderer": "^4.3.0", "@shadcn/ui": "^0.0.4", "clsx": "^2.1.1", "convex": "^1.24.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-router-dom": "^7.5.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "vcf": "^2.1.2", "zod": "^3.24.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.21.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/vcf": "^2.0.7", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}