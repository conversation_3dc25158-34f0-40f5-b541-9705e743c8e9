#!/usr/bin/env node

import { readFileSync, existsSync } from "fs";
import { join } from "path";

console.log("🔍 Produktionsreife-Check für 8IT-Verwaltung\n");

const checks = [
	{
		name: "Environment-Dateien vorhanden",
		check: () => {
			const localExists = existsSync(".env.local");
			const prodExists = existsSync(".env.production");
			return localExists && prodExists;
		},
		details: () => {
			const local = existsSync(".env.local") ? "✅" : "❌";
			const prod = existsSync(".env.production") ? "✅" : "❌";
			return `  .env.local: ${local}\n  .env.production: ${prod}`;
		},
	},
	{
		name: "Build-Ordner vorhanden",
		check: () => existsSync("dist"),
		details: () =>
			existsSync("dist")
				? "  dist/ Ordner existiert"
				: '  dist/ Ordner fehlt - führe "npm run build" aus',
	},
	{
		name: "Package.json Scripts konfiguriert",
		check: () => {
			try {
				const pkg = JSON.parse(readFileSync("package.json", "utf8"));
				const scripts = pkg.scripts;
				return scripts.dev && scripts.build && scripts.start;
			} catch {
				return false;
			}
		},
		details: () => {
			try {
				const pkg = JSON.parse(readFileSync("package.json", "utf8"));
				const scripts = pkg.scripts;
				return Object.entries(scripts)
					.filter(([key]) => ["dev", "build", "start"].includes(key))
					.map(([key, value]) => `  ${key}: ${value}`)
					.join("\n");
			} catch {
				return "  Fehler beim Lesen der package.json";
			}
		},
	},
	{
		name: "Vite-Konfiguration optimiert",
		check: () => {
			try {
				const config = readFileSync("vite.config.ts", "utf8");
				return config.includes("build:") && config.includes("outDir");
			} catch {
				return false;
			}
		},
		details: () => "  Build-Konfiguration in vite.config.ts gefunden",
	},
];

let allPassed = true;

for (const check of checks) {
	const passed = check.check();
	const status = passed ? "✅" : "❌";
	console.log(`${status} ${check.name}`);

	if (check.details) {
		console.log(check.details());
	}

	if (!passed) {
		allPassed = false;
	}

	console.log("");
}

console.log("📋 Zusammenfassung:");
if (allPassed) {
	console.log("✅ Alle Checks bestanden! Die Anwendung ist produktionsreif.");
	console.log("\n🚀 Verwendung:");
	console.log("  Development: npm run dev (verwendet .env.local)");
	console.log("  Build:       npm run build (verwendet .env.production)");
	console.log("  Production:  npm run start (verwendet .env.production)");
} else {
	console.log(
		"❌ Einige Checks fehlgeschlagen. Bitte behebe die Probleme oben.",
	);
}
