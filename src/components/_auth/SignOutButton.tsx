import { useClerk } from "@clerk/clerk-react";
import { LogOut } from "lucide-react";
import React from "react";

interface SignOutButtonProps {
	className?: string;
}

export function SignOutButton({ className }: SignOutButtonProps) {
	const { signOut } = useClerk();

	const handleSignOut = () => {
		// Redirect to sign-in page after sign out
		void signOut({ redirectUrl: "/signin" }).catch((error) => {
			console.error("Sign out error:", error);
			// Optional: Add user feedback like a toast notification
		});
	};

	return (
		<button type="button" onClick={handleSignOut} className={className}>
			<LogOut className="mr-2 h-4 w-4" />
			Abmelden
		</button>
	);
}
