import { Button } from "@/components/_shared/Button";
import { Label } from "@/components/_shared/Label";
import { Textarea } from "@/components/_shared/Textarea";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { FileText, Plus, Edit, Check } from "lucide-react";
import { useState } from "react";

interface DokumentationEntryDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (text: string) => Promise<void>;
	initialText?: string;
	isEditing?: boolean;
}

export function DokumentationEntryDialog({
	isOpen,
	onClose,
	onSubmit,
	initialText = "",
	isEditing = false,
}: DokumentationEntryDialogProps) {
	const [text, setText] = useState(initialText);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async () => {
		if (!text.trim()) return;
		
		setIsSubmitting(true);
		try {
			await onSubmit(text);
			onClose();
		} catch (error) {
			console.error("Error submitting documentation entry:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<ModalFormDialog
			isOpen={isOpen}
			onClose={onClose}
			title={isEditing ? "Dokumentationseintrag bearbeiten" : "Neuer Dokumentationseintrag"}
			description={isEditing 
				? "Bearbeiten Sie den Dokumentationseintrag" 
				: "Erstellen Sie einen neuen Dokumentationseintrag"
			}
			icon={isEditing ? <Edit className="h-3.5 w-3.5" /> : <Plus className="h-3.5 w-3.5" />}
			footerAction={{
				label: isEditing ? "Aktualisieren" : "Eintrag erstellen",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />,
				disabled: !text.trim() || isSubmitting,
				loading: isSubmitting
			}}
			maxWidth="md"
		>
			<div className="space-y-4">
				<div className="space-y-2">
					<Label 
						htmlFor="dokumentation-text" 
						className="text-sm font-medium flex items-center gap-1"
					>
						<FileText className="h-4 w-4 text-gray-400" />
						Eintrag
					</Label>
					<Textarea
						id="dokumentation-text"
						value={text}
						onChange={(e) => setText(e.target.value)}
						placeholder="Beschreiben Sie den Vorgang ausführlich..."
						className="min-h-[150px] bg-gray-800/50 border-gray-700 focus:border-blue-500"
					/>
				</div>
			</div>
		</ModalFormDialog>
	);
}