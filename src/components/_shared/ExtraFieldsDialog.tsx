import { Button } from "@/components/_shared/Button";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { FileText, X } from "lucide-react";

interface ExtraFieldsDialogProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	fields: {
		label: string;
		value: string | number | null | undefined;
	}[];
}

export function ExtraFieldsDialog({
	isOpen,
	onClose,
	title,
	fields,
}: ExtraFieldsDialogProps) {
	return (
		<ModalFormDialog
			isOpen={isOpen}
			onClose={onClose}
			title={title}
			description="Zusätzliche Informationen"
			icon={<FileText className="h-3.5 w-3.5" />}
			footer={
				<div className="flex justify-end w-full">
					<Button
						variant="outline"
						onClick={onClose}
						size="sm"
						className="h-8 text-xs"
					>
						<X className="h-3.5 w-3.5 mr-1" />
						<PERSON><PERSON><PERSON><PERSON>n
					</Button>
				</div>
			}
			maxWidth="md"
		>
			<div className="space-y-3">
				{fields.map((field, index) => (
					<div key={index} className="flex flex-col gap-1">
						<div className="text-xs font-medium text-gray-400">
							{field.label}
						</div>
						<div className="text-sm bg-gray-800/50 p-2 rounded border border-gray-700 min-h-[2rem]">
							{field.value !== undefined && field.value !== null
								? String(field.value)
								: "-"}
						</div>
					</div>
				))}
			</div>
		</ModalFormDialog>
	);
}