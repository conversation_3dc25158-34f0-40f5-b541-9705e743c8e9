import { ReactNode } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface ModalFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
  footerAction?: {
    label: string;
    onClick: () => void;
    icon?: ReactNode;
    disabled?: boolean;
    loading?: boolean;
  };
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
}

export function ModalFormDialog({
  isOpen,
  onClose,
  title,
  description,
  icon,
  children,
  footer,
  footerAction,
  maxWidth = "md",
}: ModalFormDialogProps) {
  // Map maxWidth to actual class
  const maxWidthClass = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "3xl": "max-w-3xl",
    "4xl": "max-w-4xl",
    "5xl": "max-w-5xl",
  }[maxWidth];

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-[50] flex items-center justify-center bg-black/70 p-4 overflow-y-auto"
      style={{ position: "fixed", top: 0, left: 0, right: 0, bottom: 0 }}
      onClick={(e) => {
        // Close dialog when clicking the backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className={cn(
          "w-full rounded-lg bg-gray-800 shadow-xl border border-gray-700 animate-in fade-in zoom-in-95 duration-200 my-auto relative",
          maxWidthClass
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h3 className="text-lg font-medium text-white flex items-center">
            {icon && <div className="mr-2 text-blue-400">{icon}</div>}
            {title}
          </h3>
          <button
            onClick={onClose}
            className="rounded-full p-1 text-gray-400 hover:bg-gray-700 hover:text-white"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {description && (
          <div className="px-4 pt-2 -mt-2">
            <p className="text-sm text-gray-400">{description}</p>
          </div>
        )}

        <div className="p-4 overflow-visible">
          {children}
        </div>

        <div className="flex justify-end items-center p-4 bg-gray-800 border-t border-gray-700 space-x-2">
          {footer ? (
            footer
          ) : (
            <>
              <button
                onClick={onClose}
                className="px-4 py-2 rounded-md bg-gray-700 text-gray-300 hover:bg-gray-600 focus:outline-none"
                disabled={footerAction?.loading}
              >
                Abbrechen
              </button>
              {footerAction && (
                <button
                  onClick={footerAction.onClick}
                  disabled={footerAction.disabled || footerAction.loading}
                  className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                >
                  {footerAction.loading ? (
                    "Wird verarbeitet..."
                  ) : (
                    <>
                      {footerAction.icon}
                      {footerAction.label}
                    </>
                  )}
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}