import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import {
	Briefcase,
	CalendarDays,
	ChevronDown,
	ChevronUp,
	Clock,
	Euro,
	FileOutput,
	FileText,
	Pencil,
	Trash2,
	UserCircle,
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";

// Typ übernehmen oder neu definieren (sicherstellen, dass er mit Hauptdatei übereinstimmt)
export interface LeistungMitNamen {
	_id: Id<"kunden_leistungen">;
	_creationTime: number;
	kundenId: Id<"kunden">;
	mitarbeiterId: Id<"mitarbeiter">;
	kontingentId: Id<"kunden_kontingente">;
	startZeit: number;
	endZeit: number;
	art: string;
	mitAnfahrt: boolean;
	beschreibung: string;
	stunden: number;
	stundenpreis: number;
	anfahrtskosten: number;
	kundeName: string;
	mitarbeiterName: string;
	datum: string;
	kontingentName: string;
}

interface LeistungDataTableProps {
	leistungen: LeistungMitNamen[];
	expandedDescriptions: Record<Id<"kunden_leistungen">, boolean>;
	onEdit: (leistung: LeistungMitNamen) => void;
	onDelete: (id: Id<"kunden_leistungen">) => void;
	onToggleDescription: (id: Id<"kunden_leistungen">) => void;
	onCreateLieferschein: (leistung: LeistungMitNamen) => void;
	formatDate: (timestamp: number) => string;
	formatTime: (timestamp: number) => string;
	formatHours: (hours: number) => string;
	formatCurrency: (amount: number) => string;
}

export function LeistungDataTable({
	leistungen,
	expandedDescriptions,
	onEdit,
	onDelete,
	onToggleDescription,
	onCreateLieferschein,
	formatDate,
	formatTime,
	formatHours,
	formatCurrency,
}: LeistungDataTableProps) {
	return (
		<div className="overflow-x-auto">
			<Table>
				<TableHeader>
					<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
						<TableHead className="font-medium">Datum</TableHead>
						<TableHead className="font-medium">Kunde</TableHead>
						<TableHead className="font-medium">Mitarbeiter</TableHead>
						<TableHead className="font-medium">Beschreibung</TableHead>
						<TableHead className="font-medium text-right">Stunden</TableHead>
						<TableHead className="w-24 text-center">Aktionen</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{leistungen.map((leistung) => (
						<TableRow key={leistung._id} className="border-b border-gray-800">
							<TableCell className="py-3">
								<div className="flex flex-col">
									<div className="flex items-center gap-1.5 font-medium">
										<CalendarDays className="h-3.5 w-3.5 text-gray-400" />
										{formatDate(leistung.startZeit)}
									</div>
									<div className="text-xs text-gray-400 mt-1">
										{formatTime(leistung.startZeit)} -{" "}
										{formatTime(leistung.endZeit)}
									</div>
									<div className="text-xs mt-1 inline-flex items-center">
										<span
											className={`px-1.5 py-0.5 rounded-sm text-[10px] uppercase font-medium ${
												leistung.art === "remote"
													? "bg-blue-500/20 text-blue-300"
													: leistung.art === "vor-Ort"
														? "bg-orange-500/20 text-orange-300"
														: "bg-green-500/20 text-green-300"
											}`}
										>
											{leistung.art}
										</span>
									</div>
								</div>
							</TableCell>
							<TableCell>
								<div className="flex items-center gap-2">
									<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
										<Briefcase className="h-3.5 w-3.5" />
									</div>
									<span className="font-medium">{leistung.kundeName}</span>
								</div>
								<div className="text-xs text-gray-400 mt-1 ml-9">
									{leistung.kontingentName}
								</div>
							</TableCell>
							<TableCell>
								<div className="flex items-center gap-2">
									<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
										<UserCircle className="h-3.5 w-3.5" />
									</div>
									<span>{leistung.mitarbeiterName}</span>
								</div>
							</TableCell>
							<TableCell className="max-w-xs">
								<div className="flex items-center justify-between">
									<div
										className={`whitespace-pre-wrap text-sm ${expandedDescriptions[leistung._id] ? "" : "line-clamp-2"}`}
										title={leistung.beschreibung}
									>
										{leistung.beschreibung}
									</div>
									{leistung.beschreibung.split("\n").length > 2 ||
									leistung.beschreibung.length > 100 ? (
										<button
											onClick={() => onToggleDescription(leistung._id)}
											className="ml-2 text-gray-400 hover:text-white"
											aria-label="Beschreibung ein-/ausklappen"
										>
											{expandedDescriptions[leistung._id] ? (
												<ChevronUp className="h-4 w-4" />
											) : (
												<ChevronDown className="h-4 w-4" />
											)}
										</button>
									) : null}
								</div>
							</TableCell>
							<TableCell className="text-right font-medium">
								{formatHours(leistung.stunden)}
								<div className="text-xs text-gray-400 mt-0.5">
									{formatCurrency(
										leistung.stunden * leistung.stundenpreis +
											(leistung.mitAnfahrt ? leistung.anfahrtskosten : 0),
									)}
								</div>
							</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Button
										variant="ghost"
										size="icon"
										onClick={() => onCreateLieferschein(leistung)}
										className="h-8 w-8 text-gray-400 hover:text-green-400"
										title="Lieferschein erstellen"
									>
										<FileOutput className="h-4 w-4" />
										<span className="sr-only">Lieferschein erstellen</span>
									</Button>
									<Button
										variant="ghost"
										size="icon"
										onClick={() => onEdit(leistung)}
										className="h-8 w-8 text-gray-400 hover:text-blue-400"
										title="Bearbeiten"
									>
										<Pencil className="h-4 w-4" />
										<span className="sr-only">Bearbeiten</span>
									</Button>
									<Button
										variant="ghost"
										size="icon"
										onClick={() => void onDelete(leistung._id)}
										className="h-8 w-8 text-gray-400 hover:text-red-400"
										title="Löschen"
									>
										<Trash2 className="h-4 w-4" />
										<span className="sr-only">Löschen</span>
									</Button>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
}
