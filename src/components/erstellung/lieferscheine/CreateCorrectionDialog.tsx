import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Textarea } from "@/components/_shared/Textarea";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation, useQuery } from "convex/react";
import { FilePenLine, FileText } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { LeistungCard } from "./LeistungCard";
import { TabView } from "./TabView";

interface CreateCorrectionDialogProps {
	isOpen: boolean;
	onClose: () => void;
	originalId: Id<"kunden_lieferscheine">;
	currentLeistungen: {
		_id: Id<"kunden_leistungen">;
		startZeit: number;
		endZeit: number;
		beschreibung: string;
		art: string;
		mitarbeiterName: string;
		kontingentName: string;
		stunden: number;
		inLieferscheinen?: number;
	}[];
}

export function CreateCorrectionDialog({
	isOpen,
	onClose,
	originalId,
	currentLeistungen,
}: CreateCorrectionDialogProps) {
	const [selectedLeistungIds, setSelectedLeistungIds] = useState<
		Id<"kunden_leistungen">[]
	>(currentLeistungen.map((l) => l._id));
	const [bemerkung, setBemerkung] = useState("");
	const navigate = useNavigate();

	// Laden der Lieferschein-Daten, um die Kunden-ID zu erhalten
	const lieferscheinData = useQuery(api.erstellung.lieferschein.get, {
		id: originalId,
	});

	// Laden der verfügbaren Leistungen für den Kunden
	const kundenLeistungen = useQuery(
		api.erstellung.leistung.getByKunde,
		lieferscheinData?.lieferschein
			? { kundenId: lieferscheinData.lieferschein.kundenId }
			: "skip",
	);

	// Filtern der Leistungen, die noch nicht im Lieferschein enthalten sind
	const availableLeistungen =
		kundenLeistungen?.filter(
			(leistung) => !currentLeistungen.some((cl) => cl._id === leistung._id),
		) || [];

	const createCorrection = useMutation(
		api.erstellung.lieferschein.createCorrection,
	);

	const toggleLeistung = (leistungId: Id<"kunden_leistungen">) => {
		setSelectedLeistungIds((prev) =>
			prev.includes(leistungId)
				? prev.filter((id) => id !== leistungId)
				: [...prev, leistungId],
		);
	};

	const handleSubmit = async () => {
		if (selectedLeistungIds.length === 0) {
			toast.error("Bitte wählen Sie mindestens eine Leistung aus.");
			return;
		}

		try {
			const correctionId = await createCorrection({
				originalId,
				leistungIds: selectedLeistungIds,
				bemerkung: bemerkung || undefined,
			});

			toast.success("Korrektur-Entwurf erfolgreich erstellt.");
			onClose();
			navigate(`/erstellung/lieferscheine/${correctionId}`);
		} catch (error) {
			console.error("Fehler beim Erstellen der Korrektur:", error);
			toast.error("Fehler beim Erstellen der Korrektur.");
		}
	};

	return (
		<AppDialogLayout
			isOpen={isOpen}
			onClose={onClose}
			title="Lieferschein-Korrektur-Entwurf erstellen"
			icon={<FilePenLine className="h-4 w-4 text-blue-500" />}
			description="Wählen Sie die Leistungen aus, die in der korrigierten Version enthalten sein sollen."
			maxWidth="lg"
			footerAction={{
				label: "Korrektur-Entwurf erstellen",
				onClick: handleSubmit,
				disabled: selectedLeistungIds.length === 0,
			}}
		>
			<TabView
				tabs={[
					{
						id: "current",
						title: "Vorhandene Leistungen",
						content: (
							<div className="space-y-2">
								<div className="text-xs text-gray-300 flex justify-between mb-1">
									<span className="flex items-center gap-1">
										<FileText className="h-3.5 w-3.5 text-gray-400" />
										Aktuell im Lieferschein
									</span>
									<span className="text-gray-400">
										{
											selectedLeistungIds.filter((id) =>
												currentLeistungen.some((l) => l._id === id),
											).length
										}{" "}
										von {currentLeistungen.length} ausgewählt
									</span>
								</div>

								<div className="border border-gray-800 rounded-md bg-gray-900/30 p-2 max-h-[300px] overflow-y-auto">
									{currentLeistungen.length === 0 ? (
										<div className="text-center py-4 text-sm text-gray-400">
											Keine Leistungen im Original-Lieferschein.
										</div>
									) : (
										<div className="grid grid-cols-1 gap-2">
											{currentLeistungen.map((leistung) => (
												<LeistungCard
													key={leistung._id}
													leistung={leistung}
													isSelected={selectedLeistungIds.includes(
														leistung._id,
													)}
													onClick={toggleLeistung}
												/>
											))}
										</div>
									)}
								</div>
							</div>
						),
					},
					{
						id: "add",
						title: "Weitere Leistungen",
						content: (
							<div className="space-y-2">
								<div className="text-xs text-gray-300 flex justify-between mb-1">
									<span className="flex items-center gap-1">
										<FileText className="h-3.5 w-3.5 text-gray-400" />
										Verfügbare Leistungen
									</span>
									<span className="text-gray-400">
										{
											selectedLeistungIds.filter((id) =>
												availableLeistungen.some((l) => l._id === id),
											).length
										}{" "}
										von {availableLeistungen.length} ausgewählt
									</span>
								</div>

								<div className="border border-gray-800 rounded-md bg-gray-900/30 p-2 max-h-[300px] overflow-y-auto">
									{availableLeistungen.length === 0 ? (
										<div className="text-center py-4 text-sm text-gray-400">
											Keine weiteren Leistungen für diesen Kunden verfügbar.
										</div>
									) : (
										<div className="grid grid-cols-1 gap-2">
											{availableLeistungen.map((leistung) => (
												<LeistungCard
													key={leistung._id}
													leistung={leistung}
													isSelected={selectedLeistungIds.includes(
														leistung._id,
													)}
													onClick={toggleLeistung}
												/>
											))}
										</div>
									)}
								</div>
							</div>
						),
					},
				]}
			/>

			<div className="space-y-1 mt-3">
				<label
					htmlFor="bemerkung"
					className="text-xs font-medium text-gray-300 flex items-center gap-1"
				>
					<FileText className="h-3.5 w-3.5 text-gray-400" />
					Grund der Korrektur
				</label>
				<Textarea
					id="bemerkung"
					value={bemerkung}
					onChange={(e) => setBemerkung(e.target.value)}
					placeholder="Geben Sie den Grund für die Korrektur an"
					className="h-16 text-sm bg-gray-800/50 border-gray-700 focus:border-blue-500"
				/>
			</div>

			<div className="text-xs text-gray-400 mt-2">
				{selectedLeistungIds.length} Leistung(en) für die Korrektur ausgewählt
			</div>
		</AppDialogLayout>
	);
}
