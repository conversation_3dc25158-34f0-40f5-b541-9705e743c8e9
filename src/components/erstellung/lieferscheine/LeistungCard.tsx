import { Id } from "@/../convex/_generated/dataModel";
import { Badge } from "@/components/_shared/Badge";
import { Checkbox } from "@/components/_shared/Checkbox";
import { formatDate, formatHours, formatTime } from "@/lib/utils/formatUtils";
import {
	Briefcase,
	Calendar,
	Check,
	Clock,
	FileText,
	User,
} from "lucide-react";

interface LeistungCardProps {
	leistung: {
		_id: Id<"kunden_leistungen">;
		startZeit: number;
		endZeit: number;
		beschreibung: string;
		art: string;
		mitarbeiterName: string;
		kontingentName: string;
		stunden: number;
		inLieferscheinen?: number;
	};
	isSelected: boolean;
	onClick: (leistungId: Id<"kunden_leistungen">) => void;
}

export function LeistungCard({
	leistung,
	isSelected,
	onClick,
}: LeistungCardProps) {
	return (
		<div
			className={`relative p-2 rounded border cursor-pointer ${
				isSelected
					? "border-blue-500 bg-blue-900/10"
					: "border-gray-700 bg-gray-800/50 hover:border-gray-600"
			}`}
			onClick={() => onClick(leistung._id)}
		>
			<div className="absolute top-2 left-2">
				<Checkbox
					id={`leistung-${leistung._id}`}
					checked={isSelected}
					onCheckedChange={() => onClick(leistung._id)}
					onClick={(e) => e.stopPropagation()}
					className="h-4 w-4 border-gray-600"
				/>
			</div>

			{isSelected && (
				<div className="absolute top-1 right-1">
					<Badge className="bg-blue-600 text-xs py-0 px-1 flex items-center h-5">
						<Check className="h-3 w-3 mr-0.5" />
					</Badge>
				</div>
			)}

			<div className="pl-6 pr-3 flex flex-col space-y-1">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-1 text-sm">
						<Calendar className="h-3.5 w-3.5 text-gray-400 flex-shrink-0" />
						<span className="font-medium whitespace-nowrap">
							{formatDate(leistung.startZeit)}
						</span>
						<span className="text-xs text-gray-400 whitespace-nowrap">
							{formatTime(leistung.startZeit)} - {formatTime(leistung.endZeit)}
						</span>
					</div>

					<Badge
						className={`text-xs py-0 px-1.5 h-5 ml-1 ${
							leistung.art === "remote"
								? "bg-blue-900/30 text-blue-300 border border-blue-800"
								: leistung.art === "vor-Ort"
									? "bg-orange-900/30 text-orange-300 border border-orange-800"
									: "bg-green-900/30 text-green-300 border border-green-800"
						}`}
					>
						{leistung.art}
					</Badge>
				</div>

				<div className="text-xs">
					{leistung.beschreibung.length > 100
						? `${leistung.beschreibung.substring(0, 100)}...`
						: leistung.beschreibung}
				</div>

				<div className="flex flex-wrap text-xs gap-x-3 gap-y-0.5 mt-1">
					<div className="flex items-center gap-1 text-gray-300">
						<User className="h-3 w-3 text-gray-400" />
						<span>{leistung.mitarbeiterName}</span>
					</div>
					<div className="flex items-center gap-1 text-gray-300">
						<Briefcase className="h-3 w-3 text-gray-400" />
						<span>{leistung.kontingentName}</span>
					</div>
					<div className="flex items-center gap-1">
						<Clock className="h-3 w-3 text-gray-400" />
						<span>{formatHours(leistung.stunden)}</span>
					</div>

					{leistung.inLieferscheinen && leistung.inLieferscheinen > 0 && (
						<div className="flex items-center gap-1 text-blue-300">
							<FileText className="h-3 w-3 text-blue-400" />
							<span>
								In {leistung.inLieferscheinen}{" "}
								{leistung.inLieferscheinen === 1
									? "Lieferschein"
									: "Lieferscheinen"}
							</span>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
