import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { EmptyState } from "@/components/layout/EmptyState";
import { FileText, Filter } from "lucide-react";
import { useState } from "react";
import { FilterBar } from "./FilterBar";
import { LeistungCard } from "./LeistungCard";

interface LeistungListProps {
	leistungen: Array<{
		_id: Id<"kunden_leistungen">;
		startZeit: number;
		endZeit: number;
		beschreibung: string;
		art: string;
		mitarbeiterName: string;
		kontingentName: string;
		stunden: number;
		inLieferscheinen?: number;
	}>;
	selectedIds: Id<"kunden_leistungen">[];
	onToggleLeistung: (leistungId: Id<"kunden_leistungen">) => void;
	onToggleAll: () => void;
	emptyMessage?: string;
}

export function LeistungList({
	leistungen,
	selectedIds,
	onToggleLeistung,
	onToggleAll,
	emptyMessage = "Keine Leistungen verfügbar.",
}: LeistungListProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [sortBy, setSortBy] = useState<"date" | "type" | "hours">("date");

	// Apply search and sort
	const filteredLeistungen = leistungen
		.filter((l) => {
			if (!searchTerm.trim()) return true;

			const search = searchTerm.toLowerCase();
			return (
				l.beschreibung.toLowerCase().includes(search) ||
				l.mitarbeiterName.toLowerCase().includes(search) ||
				l.kontingentName.toLowerCase().includes(search) ||
				l.art.toLowerCase().includes(search)
			);
		})
		.sort((a, b) => {
			if (sortBy === "date") {
				return b.startZeit - a.startZeit;
			} else if (sortBy === "hours") {
				return b.stunden - a.stunden;
			} else if (sortBy === "type") {
				return a.art.localeCompare(b.art);
			}
			return 0;
		});

	// Sort options component for the filter bar
	const sortOptions = (
		<div className="flex items-center gap-1 text-xs">
			<span className="text-gray-400 flex items-center gap-1">
				<Filter className="h-3 w-3" />
			</span>
			<button
				className={`px-1.5 py-0.5 rounded ${
					sortBy === "date"
						? "bg-blue-900/30 text-blue-300 border border-blue-800"
						: "text-gray-400 hover:text-white"
				}`}
				onClick={() => setSortBy("date")}
			>
				Datum
			</button>
			<button
				className={`px-1.5 py-0.5 rounded ${
					sortBy === "type"
						? "bg-blue-900/30 text-blue-300 border border-blue-800"
						: "text-gray-400 hover:text-white"
				}`}
				onClick={() => setSortBy("type")}
			>
				Art
			</button>
			<button
				className={`px-1.5 py-0.5 rounded ${
					sortBy === "hours"
						? "bg-blue-900/30 text-blue-300 border border-blue-800"
						: "text-gray-400 hover:text-white"
				}`}
				onClick={() => setSortBy("hours")}
			>
				Std
			</button>
		</div>
	);

	return (
		<div className="flex flex-col">
			<div className="px-3 pb-1">
				<FilterBar
					searchValue={searchTerm}
					onSearchChange={setSearchTerm}
					placeholder="Leistungen suchen..."
					rightSection={sortOptions}
				/>

				<div className="flex justify-between items-center">
					<span className="text-xs text-gray-300 flex items-center gap-1">
						<FileText className="h-3.5 w-3.5 text-gray-400" />
						Leistungen
					</span>
					<span className="text-xs text-gray-400">
						{selectedIds.length} von {filteredLeistungen.length} ausgewählt
					</span>
				</div>
			</div>

			<div className="border-t border-b border-gray-800 bg-gray-900/30 overflow-y-auto max-h-[350px]">
				{filteredLeistungen.length === 0 ? (
					<EmptyState
						icon={<FileText className="h-8 w-8" />}
						title=""
						message={
							searchTerm
								? "Keine Leistungen für diesen Suchbegriff gefunden."
								: emptyMessage
						}
					/>
				) : (
					<div className="grid grid-cols-1 gap-2 p-2">
						{filteredLeistungen.map((leistung) => (
							<LeistungCard
								key={leistung._id}
								leistung={leistung}
								isSelected={selectedIds.includes(leistung._id)}
								onClick={onToggleLeistung}
							/>
						))}
					</div>
				)}
			</div>

			{filteredLeistungen.length > 0 && (
				<div className="flex justify-start px-3 pt-2">
					<Button
						variant="outline"
						size="sm"
						onClick={onToggleAll}
						className="text-xs py-1 h-7"
					>
						{selectedIds.length === filteredLeistungen.length
							? "Alle abwählen"
							: "Alle auswählen"}
					</Button>
				</div>
			)}
		</div>
	);
}
