import { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { TableCell, TableRow } from "@/components/_shared/Table";
import { EmailButton } from "@/components/system/email";
import {
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	FileText,
	Trash2,
} from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

interface LieferscheinRowProps {
	lieferschein: Doc<"kunden_lieferscheine"> & {
		kundeName: string;
		erstelltAmFormatiert: string;
		hatKorrektur: boolean;
	};
	korrekturen: Array<
		Doc<"kunden_lieferscheine"> & {
			kundeName: string;
			erstelltAmFormatiert: string;
			hatKorrektur: boolean;
		}
	>;
	original:
		| (Doc<"kunden_lieferscheine"> & {
				kundeName: string;
				erstelltAmFormatiert: string;
				hatKorrektur: boolean;
		  })
		| null;
	onDelete: (id: Id<"kunden_lieferscheine">) => void;
	isExpanded: boolean;
	onToggleExpand: () => void;
}

export function LieferscheinRow({
	lieferschein,
	korrekturen,
	original,
	onDelete,
	isExpanded,
	onToggleExpand,
}: LieferscheinRowProps) {
	// Bestimme, ob es Korrekturen oder ein Original gibt, die angezeigt werden können
	const hasRelatedDocuments = korrekturen.length > 0 || original !== null;

	return (
		<>
			{/* Hauptzeile */}
			<TableRow className="border-b border-gray-800">
				<TableCell className="font-medium">
					<div className="flex items-center">
						{hasRelatedDocuments && (
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6 mr-2 text-gray-400 hover:text-gray-300"
								onClick={onToggleExpand}
								title={isExpanded ? "Einklappen" : "Ausklappen"}
							>
								{isExpanded ? (
									<ChevronUp className="h-4 w-4" />
								) : (
									<ChevronDown className="h-4 w-4" />
								)}
							</Button>
						)}
						<span>
							{lieferschein.nummer}
							{lieferschein.istKorrektur && (
								<span className="ml-2 text-xs text-orange-400">
									(Korrektur)
								</span>
							)}
						</span>
					</div>
				</TableCell>
				<TableCell>{lieferschein.kundeName}</TableCell>
				<TableCell>{lieferschein.erstelltAmFormatiert}</TableCell>
				<TableCell>
					{lieferschein.status === "entwurf" ? (
						<div className="flex items-center text-blue-400">
							<FileText className="h-4 w-4 mr-1" />
							<span>Entwurf</span>
						</div>
					) : lieferschein.hatKorrektur ? (
						<div className="flex items-center text-orange-400">
							<AlertTriangle className="h-4 w-4 mr-1" />
							<span>Korrigiert</span>
						</div>
					) : (
						<span className="text-green-400">Fertig</span>
					)}
				</TableCell>
				<TableCell>
					<div className="flex justify-center gap-1">
						<Link
							to={`/erstellung/lieferscheine/${lieferschein.nummer || lieferschein._id}`}
						>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 text-gray-400 hover:text-blue-400"
								title="Anzeigen"
							>
								<FileText className="h-4 w-4" />
								<span className="sr-only">Anzeigen</span>
							</Button>
						</Link>
						{lieferschein.status === "fertig" && (
							<EmailButton 
								type="lieferschein"
								lieferscheinId={lieferschein._id}
								tooltipText="Per E-Mail versenden"
							/>
						)}
						{!lieferschein.istKorrektur && lieferschein.status === "fertig" && (
							<Link
								to={`/erstellung/lieferscheine/${lieferschein.nummer || lieferschein._id}`}
								state={{ openCorrectionDialog: true }}
							>
								<Button
									variant="ghost"
									size="icon"
									className="h-8 w-8 text-gray-400 hover:text-orange-400"
									title="Korrektur erstellen"
								>
									<AlertTriangle className="h-4 w-4" />
									<span className="sr-only">Korrektur</span>
								</Button>
							</Link>
						)}
						{/* Lösch-Button für Entwürfe und Korrekturen im Entwurfsstatus */}
						{lieferschein.status === "entwurf" && (
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 text-gray-400 hover:text-red-400"
								title="Löschen"
								onClick={() => onDelete(lieferschein._id)}
							>
								<Trash2 className="h-4 w-4" />
								<span className="sr-only">Löschen</span>
							</Button>
						)}
					</div>
				</TableCell>
			</TableRow>

			{/* Untergeordnete Zeilen (Korrekturen und Original) */}
			{isExpanded && (
				<>
					{/* Andere Korrekturen (außer der neuesten) */}
					{korrekturen.map((korrektur) => (
						<TableRow
							key={korrektur._id}
							className="border-b border-gray-800 bg-gray-800/20"
						>
							<TableCell className="font-medium pl-10">
								{korrektur.nummer}
								<span className="ml-2 text-xs text-orange-400">
									(Korrektur)
								</span>
							</TableCell>
							<TableCell>{korrektur.kundeName}</TableCell>
							<TableCell>{korrektur.erstelltAmFormatiert}</TableCell>
							<TableCell>
								{korrektur.status === "entwurf" ? (
									<div className="flex items-center text-blue-400">
										<FileText className="h-4 w-4 mr-1" />
										<span>Entwurf</span>
									</div>
								) : (
									<span className="text-green-400">Fertig</span>
								)}
							</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Link
										to={`/erstellung/lieferscheine/${korrektur.nummer || korrektur._id}`}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Anzeigen"
										>
											<FileText className="h-4 w-4" />
											<span className="sr-only">Anzeigen</span>
										</Button>
									</Link>
									{korrektur.status === "fertig" && (
										<EmailButton 
											type="lieferschein"
											lieferscheinId={korrektur._id}
											tooltipText="Per E-Mail versenden"
										/>
									)}
									{korrektur.status === "entwurf" && (
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-red-400"
											title="Löschen"
											onClick={() => onDelete(korrektur._id)}
										>
											<Trash2 className="h-4 w-4" />
											<span className="sr-only">Löschen</span>
										</Button>
									)}
								</div>
							</TableCell>
						</TableRow>
					))}

					{/* Original-Dokument (ganz unten) */}
					{original && (
						<TableRow
							key={original._id}
							className="border-b border-gray-800 bg-gray-800/30"
						>
							<TableCell className="font-medium pl-10">
								{original.nummer}
								<span className="ml-2 text-xs text-gray-400">(Original)</span>
							</TableCell>
							<TableCell>{original.kundeName}</TableCell>
							<TableCell>{original.erstelltAmFormatiert}</TableCell>
							<TableCell>
								<span className="text-green-400">Fertig</span>
							</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Link
										to={`/erstellung/lieferscheine/${original.nummer || original._id}`}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Anzeigen"
										>
											<FileText className="h-4 w-4" />
											<span className="sr-only">Anzeigen</span>
										</Button>
									</Link>
									{/* Email button for the original */}
									<EmailButton 
										type="lieferschein"
										lieferscheinId={original._id}
										tooltipText="Per E-Mail versenden"
									/>
									{/* Korrektur-Button für das Original */}
									<Link
										to={`/erstellung/lieferscheine/${original.nummer || original._id}`}
										state={{ openCorrectionDialog: true }}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-orange-400"
											title="Korrektur erstellen"
										>
											<AlertTriangle className="h-4 w-4" />
											<span className="sr-only">Korrektur</span>
										</Button>
									</Link>
								</div>
							</TableCell>
						</TableRow>
					)}
				</>
			)}
		</>
	);
}
