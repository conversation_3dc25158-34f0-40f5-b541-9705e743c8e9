import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation, useQuery } from "convex/react";
import { FileText, Plus, User } from "lucide-react";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import { LeistungList } from "./LeistungList";

interface NewLieferscheinDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

export function NewLieferscheinDialog({
	isOpen,
	onClose,
}: NewLieferscheinDialogProps) {
	const [kundeId, setKundeId] = useState<Id<"kunden"> | "">("");
	const [selectedLeistungen, setSelectedLeistungen] = useState<
		Id<"kunden_leistungen">[]
	>([]);
	const [bemerkung, setBemerkung] = useState("");

	const kunden = useQuery(api.kunden.stammdaten.list) || [];
	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
	const createLieferschein = useMutation(api.erstellung.lieferschein.create);

	// Filter leistungen by selected kunde
	const kundenLeistungen = useMemo(() => {
		if (!kundeId) return [];
		return allLeistungen.filter((l) => l.kundenId === kundeId);
	}, [allLeistungen, kundeId]);

	const handleSubmit = async () => {
		if (!kundeId) {
			toast.error("Bitte wählen Sie einen Kunden aus.");
			return;
		}

		try {
			const lieferscheinId = await createLieferschein({
				kundenId: kundeId,
				leistungIds: selectedLeistungen,
				bemerkung: bemerkung || undefined,
			});

			toast.success("Lieferschein-Entwurf erfolgreich erstellt.");
			resetForm();
			onClose();

			// Zur Detailseite navigieren
			if (lieferscheinId) {
				window.location.href = `/erstellung/lieferscheine/${lieferscheinId}`;
			}
		} catch (error) {
			console.error("Fehler beim Erstellen des Lieferscheins:", error);
			toast.error("Fehler beim Erstellen des Lieferscheins.");
		}
	};

	const resetForm = () => {
		setKundeId("");
		setSelectedLeistungen([]);
		setBemerkung("");
	};

	const toggleLeistung = (leistungId: Id<"kunden_leistungen">) => {
		setSelectedLeistungen((prev) =>
			prev.includes(leistungId)
				? prev.filter((id) => id !== leistungId)
				: [...prev, leistungId],
		);
	};

	const toggleAllLeistungen = () => {
		if (selectedLeistungen.length === kundenLeistungen.length) {
			setSelectedLeistungen([]);
		} else {
			setSelectedLeistungen(kundenLeistungen.map((l) => l._id));
		}
	};

	return (
		<AppDialogLayout
			isOpen={isOpen}
			onClose={onClose}
			title="Neuen Lieferschein-Entwurf erstellen"
			description="Wählen Sie einen Kunden aus und optional Leistungen, die auf dem Lieferschein erscheinen sollen."
			maxWidth="lg"
			footerAction={{
				label:
					selectedLeistungen.length > 0
						? `Lieferschein mit ${selectedLeistungen.length} Leistung${selectedLeistungen.length !== 1 ? "en" : ""} erstellen`
						: "Leeren Lieferschein-Entwurf erstellen",
				onClick: handleSubmit,
				icon: <Plus className="h-3.5 w-3.5" />,
				disabled: !kundeId,
			}}
		>
			<div className="space-y-4">
				<div className="space-y-1">
					<label
						htmlFor="kundeId"
						className="text-sm font-medium text-gray-300 flex items-center gap-1"
					>
						<User className="h-3.5 w-3.5 text-gray-400" />
						Kunde
					</label>
					<Select
						value={kundeId}
						onValueChange={(value: string) => {
							setKundeId(value as Id<"kunden">);
							setSelectedLeistungen([]);
						}}
					>
						<SelectTrigger id="kundeId" className="w-full">
							<SelectValue placeholder="Kunde auswählen" />
						</SelectTrigger>
						<SelectContent>
							{kunden.map((kunde) => (
								<SelectItem key={kunde._id} value={kunde._id}>
									{kunde.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{kundeId && (
					<div className="space-y-1">
						<LeistungList
							leistungen={kundenLeistungen}
							selectedIds={selectedLeistungen}
							onToggleLeistung={toggleLeistung}
							onToggleAll={toggleAllLeistungen}
							emptyMessage="Keine Leistungen für diesen Kunden vorhanden."
						/>
					</div>
				)}

				<div className="space-y-1 mt-3">
					<label
						htmlFor="bemerkung"
						className="text-sm font-medium text-gray-300 flex items-center gap-1"
					>
						<FileText className="h-3.5 w-3.5 text-gray-400" />
						Bemerkung (optional)
					</label>
					<Textarea
						id="bemerkung"
						value={bemerkung}
						onChange={(e) => setBemerkung(e.target.value)}
						placeholder="Zusätzliche Informationen zum Lieferschein"
						className="h-16 bg-gray-800/50 border-gray-700 focus:border-blue-500 text-sm"
					/>
				</div>
			</div>
		</AppDialogLayout>
	);
}
