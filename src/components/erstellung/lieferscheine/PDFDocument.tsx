import { Id } from "@/../convex/_generated/dataModel";
import {
	Document,
	Font,
	Image,
	Page,
	StyleSheet,
	Text,
	View,
} from "@react-pdf/renderer";
import React from "react";

// Register fonts
Font.register({
	family: "Roboto",
	fonts: [
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf",
			fontWeight: 400,
		},
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf",
			fontWeight: 500,
		},
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf",
			fontWeight: 700,
		},
	],
});

// Define styles
const styles = StyleSheet.create({
	page: {
		paddingHorizontal: 30, // <PERSON><PERSON><PERSON>t von uebersicht
		paddingTop: 30, // <PERSON><PERSON><PERSON><PERSON> von uebersicht
		paddingBottom: 60, // <PERSON><PERSON><PERSON><PERSON> von uebersicht (für Footer)
		fontFamily: "Roboto",
		fontSize: 10, // Beibehalten von Lieferschein, da uebersicht keine globale fontSize hat
		color: "#333333",
		backgroundColor: "#FFFFFF", // Angepasst von uebersicht
	},
	headerContainer: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		marginBottom: 15, // Angepasst von uebersicht
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
		paddingBottom: 8,
	},
	headerTextContainer: {
		flex: 1,
		flexDirection: "column",
	},
	headerLogoContainer: {
		width: 200,
		height: 100,
		justifyContent: "center",
		alignItems: "center",
	},
	logo: {
		width: 200,
		height: 100,
		objectFit: "contain",
	},
	title: {
		fontSize: 16, // Angepasst von uebersicht
		fontWeight: 700,
		color: "#333333", // Angepasst von uebersicht
		marginBottom: 4, // Angepasst von uebersicht
	},
	subtitle: {
		fontSize: 10, // Angepasst von uebersicht
		color: "#555555", // Angepasst von uebersicht
		marginBottom: 2, // Angepasst von uebersicht
	},
	section: {
		marginTop: 8, // Angepasst von uebersicht
		marginBottom: 8, // Angepasst von uebersicht
	},
	sectionTitle: {
		fontSize: 12, // Angepasst von uebersicht
		fontWeight: 700,
		color: "#444444", // Angepasst von uebersicht
		marginBottom: 6, // Angepasst von uebersicht
		paddingBottom: 3, // Angepasst von uebersicht
		borderBottomWidth: 0.5, // Angepasst von uebersicht
		borderBottomColor: "#DDDDDD", // Angepasst von uebersicht
	},
	table: {
		// Hinzugefügt von uebersicht
		display: "flex",
		width: "auto",
		borderStyle: "solid",
		borderWidth: 0.5,
		borderColor: "#DDDDDD",
		marginBottom: 8,
	},
	tableRowHeader: {
		// Angepasst von uebersicht (statt tableHeader)
		flexDirection: "row",
		backgroundColor: "#F5F5F5",
		fontWeight: 700, // fontWeight hier, nicht auf einzelnen Text-Elementen
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
		alignItems: "stretch",
		minHeight: 18,
	},
	tableRow: {
		flexDirection: "row",
		borderBottomWidth: 0.5, // Angepasst von uebersicht
		borderBottomColor: "#DDDDDD", // Angepasst von uebersicht
		alignItems: "stretch", // Angepasst von uebersicht
		minHeight: 18, // Angepasst von uebersicht
		// paddingVertical entfernt, da tableCol padding hat
	},
	tableCol: {
		padding: 4, // Angepasst von uebersicht
		fontSize: 9, // Angepasst von uebersicht
		color: "#333333", // Angepasst von uebersicht
	},
	tableColLast: {
		// Keine spezielle Formatierung mehr nötig
	},
	// Column widths updated to match Übersichten
	colLeistungDatumKontingent: { width: "20%" },
	colLeistungZeitArt: { width: "15%" },
	colLeistungMitarbeiterStunden: { width: "20%" },
	colLeistungBeschreibung: { width: "45%" },
	textMuted: {
		fontSize: 8, // Angepasst von uebersicht
		color: "#777777", // Angepasst von uebersicht
		marginTop: 1, // Angepasst von uebersicht
	},
	summaryRow: {
		// Beibehalten, da Lieferschein keine explizite Summary Section hat wie Übersicht
		flexDirection: "row",
		justifyContent: "space-between",
		paddingVertical: 3,
	},
	summaryLabel: {
		fontWeight: 700,
	},
	summaryValue: {
		textAlign: "right",
	},
	footer: {
		position: "absolute",
		bottom: 25, // Angepasst von uebersicht
		left: 30, // Angepasst von uebersicht
		right: 30, // Angepasst von uebersicht
		fontSize: 8,
		textAlign: "center",
		color: "#888888", // Angepasst von uebersicht
		borderTopWidth: 0.5, // Angepasst von uebersicht
		borderTopColor: "#DDDDDD", // Angepasst von uebersicht
		paddingTop: 6, // Angepasst von uebersicht
	},
	legalText: {
		marginTop: 12, // Angelehnt an summarySection von uebersicht
		padding: 10,
		backgroundColor: "#F5F5F5",
		borderRadius: 5,
		marginBottom: 8,
	},
	legalTextContent: {
		fontSize: 8,
		color: "#666666",
		lineHeight: 1.4,
	},
	bemerkung: {
		marginTop: 12, // Angelehnt an summarySection von uebersicht
		padding: 10,
		backgroundColor: "#F9F9F9",
		borderRadius: 5,
		marginBottom: 8,
	},
	bemerkungTitle: {
		fontWeight: 700,
		marginBottom: 5,
	},
	signatureContainer: {
		// Styles für Unterschrift von uebersicht nicht direkt übertragbar, da Struktur anders.
		marginTop: 40, // Etwas reduziert, aber immer noch deutlich
		marginBottom: 20,
		paddingHorizontal: 0, // Keine horizontale Polsterung, um Linie über volle Breite zu ermöglichen, falls gewünscht
	},
	signatureLine: {
		borderTopWidth: 0.5, // Dünnere Linie
		borderTopColor: "#AAAAAA", // Hellere Farbe
		width: "45%", // Kürzere Linie, zentriert oder linksbündig je nach Layout
		marginBottom: 3,
	},
	signatureText: {
		fontSize: 8,
		color: "#777777", // Hellere Farbe
	},
	pageNumber: {
		// Hinzugefügt von uebersicht
		position: "absolute",
		fontSize: 8,
		bottom: 8,
		left: 0,
		right: 30,
		textAlign: "right",
		color: "#888888",
	},
});

interface PDFLieferscheinProps {
	lieferschein: {
		_id: Id<"kunden_lieferscheine">;
		nummer: string;
		erstelltAm: number;
		erstelltAmFormatiert: string;
		istKorrektur: boolean;
		bemerkung?: string;
		kundeName: string;
	};
	leistungen: Array<{
		_id: Id<"kunden_leistungen">;
		startZeit: number;
		endZeit: number;
		art: string;
		beschreibung: string;
		stunden: number;
		stundenpreis: number;
		anfahrtskosten: number;
		mitAnfahrt: boolean;
		datum: string;
		startZeitFormatiert: string;
		endZeitFormatiert: string;
		kontingentName?: string;
		mitarbeiterName?: string;
	}>;
	includeHeader: boolean;
	includeFooter: boolean;
	showLogo: boolean;
	includeLegalText: boolean;
	includeSignatureField: boolean;
	logoUrl?: string;
	firmenName?: string;
	firmenFusszeileText?: string;
	legalText?: string;
	signatureText?: string;
	formatCurrency: (amount: number) => string;
	formatHours: (hours: number) => string;
}

export const PDFLieferscheinDocument = ({
	lieferschein,
	leistungen,
	includeHeader,
	includeFooter,
	showLogo,
	includeLegalText,
	includeSignatureField,
	logoUrl,
	firmenName = "innov8-IT",
	firmenFusszeileText = "© innov8-IT",
	legalText = "",
	signatureText = "Ort / Datum / Unterschrift / Stempel",
	formatCurrency,
	formatHours,
}: PDFLieferscheinProps) => {
	// Keine Preisberechnung für Lieferscheine

	const documentTitle = lieferschein.istKorrektur
		? `Lieferschein (Korrektur) ${lieferschein.nummer}`
		: `Lieferschein ${lieferschein.nummer}`;

	return (
		<Document author={firmenName} title={documentTitle}>
			<Page size="A4" style={styles.page}>
				{includeHeader && (
					<View style={styles.headerContainer}>
						<View style={styles.headerTextContainer}>
							<Text style={styles.title}>{documentTitle}</Text>
							<Text style={styles.subtitle}>
								Kunde: {lieferschein.kundeName}
							</Text>
							<Text style={styles.subtitle}>
								Datum: {lieferschein.erstelltAmFormatiert}
							</Text>
						</View>
						<View style={styles.headerLogoContainer}>
							{showLogo && logoUrl ? (
								<Image style={styles.logo} src={logoUrl} />
							) : showLogo ? (
								<Text
									style={{
										fontSize: 14,
										color: "#007AFF",
										fontWeight: "bold",
									}}
								>
									innov<Text style={{ color: "#50E3C2" }}>8</Text>-IT
								</Text>
							) : null}
						</View>
					</View>
				)}

				{/* Leistungen */}
				<View style={styles.section} wrap={false}>
					<Text style={styles.sectionTitle}>Erbrachte Leistungen</Text>
					<View style={styles.table}>
						<View fixed style={[styles.tableRow, styles.tableRowHeader]}>
							<Text
								style={[styles.tableCol, styles.colLeistungDatumKontingent]}
							>
								Datum / Kontingent
							</Text>
							<Text style={[styles.tableCol, styles.colLeistungZeitArt]}>
								Zeit / Art
							</Text>
							<Text
								style={[styles.tableCol, styles.colLeistungMitarbeiterStunden]}
							>
								Mitarbeiter / Std.
							</Text>
							<Text
								style={[
									styles.tableCol,
									styles.colLeistungBeschreibung,
									styles.tableColLast,
								]}
							>
								Beschreibung
							</Text>
						</View>
						{leistungen.map((leistung) => {
							return (
								<View key={leistung._id} style={styles.tableRow}>
									<View
										style={[styles.tableCol, styles.colLeistungDatumKontingent]}
									>
										<Text>{leistung.datum}</Text>
										{leistung.kontingentName && (
											<Text style={styles.textMuted}>
												{leistung.kontingentName}
											</Text>
										)}
									</View>
									<View style={[styles.tableCol, styles.colLeistungZeitArt]}>
										<Text>
											{leistung.startZeitFormatiert} -{" "}
											{leistung.endZeitFormatiert}
										</Text>
										<Text style={styles.textMuted}>{leistung.art}</Text>
									</View>
									<View
										style={[
											styles.tableCol,
											styles.colLeistungMitarbeiterStunden,
										]}
									>
										<Text>{leistung.mitarbeiterName || " "}</Text>
										<Text style={styles.textMuted}>
											{formatHours(leistung.stunden)}
										</Text>
									</View>
									<Text
										style={[
											styles.tableCol,
											styles.colLeistungBeschreibung,
											styles.tableColLast,
										]}
									>
										{leistung.beschreibung}
									</Text>
								</View>
							);
						})}
					</View>
				</View>

				{/* Legal Text */}
				{includeLegalText && legalText && (
					<View style={styles.legalText}>
						<Text style={styles.legalTextContent}>{legalText}</Text>
					</View>
				)}

				{/* Bemerkung */}
				{lieferschein.bemerkung && (
					<View style={styles.bemerkung}>
						<Text style={styles.bemerkungTitle}>Bemerkung:</Text>
						<Text>{lieferschein.bemerkung}</Text>
					</View>
				)}

				{/* Unterschriftsfeld */}
				{includeSignatureField && (
					<View style={styles.signatureContainer}>
						<View style={styles.signatureLine}></View>
						<Text style={styles.signatureText}>{signatureText}</Text>
					</View>
				)}

				{/* Footer */}
				{includeFooter && (
					<View style={styles.footer} fixed>
						<Text>{firmenFusszeileText}</Text>
					</View>
				)}
				<Text
					style={styles.pageNumber}
					render={({ pageNumber, totalPages }) =>
						`${pageNumber} / ${totalPages}`
					}
					fixed
				/>
			</Page>
		</Document>
	);
};
