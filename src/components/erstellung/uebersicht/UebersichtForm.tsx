import { api } from "@/../convex/_generated/api";
import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import {
	DateRangeOption,
	calculateDateRange,
	toISODateString,
} from "@/lib/utils/dateUtils";
import { useQuery } from "convex/react";
import { CalendarDays, Filter } from "lucide-react";
import { useEffect } from "react";
import type { Kunde } from "../../../pages/erstellung/uebersicht/types"; // Use type import
import type { UebersichtFormState } from "../../../pages/erstellung/uebersicht/types";

interface UebersichtFormProps {
	formState: UebersichtFormState;
	onFormChange: (key: keyof UebersichtFormState, value: any) => void;
	onSubmit: () => void;
	buttonLabel?: string;
}

export function UebersichtForm({
	formState,
	onFormChange,
	onSubmit,
	buttonLabel = "Anzeigen",
}: UebersichtFormProps) {
	const kunden = useQuery(api.kunden.stammdaten.list) || [];

	// Helper function to set date ranges based on predefined options
	useEffect(() => {
		if (formState.dateRange !== "custom") {
			// Verwende die date-fns Hilfsfunktion für präzise Datumsberechnungen
			const { startDate, endDate } = calculateDateRange(formState.dateRange);

			// Konvertiere die Daten in ISO-Strings für die Formularfelder
			onFormChange("startDatum", toISODateString(startDate));
			onFormChange("endDatum", toISODateString(endDate));
		}
	}, [formState.dateRange]); // Removed onFormChange from dependency array

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit();
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-4">
			<div className="space-y-3">
				{/* Kunde Selection */}
				<div className="space-y-1.5">
					<Label htmlFor="kundeId">Kunde</Label>
					<Select
						value={formState.kundeId}
						onValueChange={(value) => onFormChange("kundeId", value)}
						required
					>
						<SelectTrigger className="h-9 bg-gray-800/60 border-gray-700">
							<SelectValue placeholder="Kunde auswählen" />
						</SelectTrigger>
						<SelectContent>
							{kunden.map((kunde: Kunde) => (
								<SelectItem key={kunde._id} value={kunde._id}>
									{kunde.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Date Range Selection */}
				<div className="space-y-1.5">
					<Label htmlFor="dateRange">Zeitraum</Label>
					<Select
						value={formState.dateRange}
						onValueChange={(value) =>
							onFormChange("dateRange", value as DateRangeOption)
						}
						required
					>
						<SelectTrigger className="h-9 bg-gray-800/60 border-gray-700">
							<SelectValue placeholder="Zeitraum auswählen" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="thisMonth">Dieser Monat</SelectItem>
							<SelectItem value="lastMonth">Letzter Monat</SelectItem>
							<SelectItem value="thisQuarter">Dieses Quartal</SelectItem>
							<SelectItem value="lastQuarter">Letztes Quartal</SelectItem>
							<SelectItem value="thisYear">Dieses Jahr</SelectItem>
							<SelectItem value="lastYear">Letztes Jahr</SelectItem>
							<SelectItem value="custom">Benutzerdefiniert</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Custom Date Range */}
			{formState.dateRange === "custom" && (
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-1.5">
						<Label htmlFor="startDatum">Startdatum</Label>
						<div className="relative">
							<CalendarDays className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<Input
								id="startDatum"
								type="date"
								value={formState.startDatum}
								onChange={(e) => onFormChange("startDatum", e.target.value)}
								required
								className="h-9 pl-8 bg-gray-800/60 border-gray-700"
							/>
						</div>
					</div>
					<div className="space-y-1.5">
						<Label htmlFor="endDatum">Enddatum</Label>
						<div className="relative">
							<CalendarDays className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<Input
								id="endDatum"
								type="date"
								value={formState.endDatum}
								onChange={(e) => onFormChange("endDatum", e.target.value)}
								required
								className="h-9 pl-8 bg-gray-800/60 border-gray-700"
							/>
						</div>
					</div>
				</div>
			)}

			<div className="flex justify-end">
				<Button type="submit" className="gap-1.5" disabled={!formState.kundeId}>
					<Filter className="h-4 w-4" />
					{buttonLabel}
				</Button>
			</div>
		</form>
	);
}

export default UebersichtForm;
