import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/_shared/Dialog";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { useMutation, useQuery } from "convex/react";
import {
	CheckCircle,
	ChevronDown,
	ChevronUp,
	Copy,
	ExternalLink,
	Eye,
	EyeOff,
	MoreHorizontal,
	Pencil,
	Trash2,
	X,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface KategorieFeld {
	feldId: number;
	name: string;
	typ: string;
	istErforderlich: boolean;
	optionen?: string[];
	istKopierbar?: boolean;
	istVersteckt?: boolean;
	istExtrafeld?: boolean;
}

interface DokuEintrag {
	_id: Id<"kunden_dokumentation">;
	kategorieId: Id<"system_doku_kategorien">;
	feldwerte: { feldId: number; feldWert: string }[];
	notizen?: string;
}

interface DokuCategoryListProps {
	kategorie: {
		_id: Id<"system_doku_kategorien">;
		name: string;
		beschreibung: string;
		felder: KategorieFeld[];
	};
	eintraege: DokuEintrag[];
	onEdit: (eintragId: Id<"kunden_dokumentation">) => void;
	kundenId: Id<"kunden">;
}

export function DokuCategoryList({
	kategorie,
	eintraege,
	onEdit,
	kundenId,
}: DokuCategoryListProps) {
	const removeEintrag = useMutation(api.kunden.dokumentation.remove);

	const [passwordVisibility, setPasswordVisibility] = useState<
		Record<string, boolean>
	>({});
	const [expandedNotizen, setExpandedNotizen] = useState<
		Record<string, boolean>
	>({});
	const [copiedStatus, setCopiedStatus] = useState<Record<string, boolean>>({});
	const [extraFieldsModalOpen, setExtraFieldsModalOpen] = useState<
		Record<string, boolean>
	>({});
	const [currentExtraFieldsEntry, setCurrentExtraFieldsEntry] =
		useState<DokuEintrag | null>(null);

	// Toggle password visibility
	const togglePasswordVisibility = (entryId: string, feldId: number) => {
		const key = `${entryId}-${feldId}`;
		setPasswordVisibility((prev) => ({
			...prev,
			[key]: !prev[key],
		}));
	};

	const toggleNotizen = (entryId: string) => {
		setExpandedNotizen((prev) => ({ ...prev, [entryId]: !prev[entryId] }));
	};

	// Open extra fields modal
	const openExtraFieldsModal = (entryId: string, entry: DokuEintrag) => {
		setExtraFieldsModalOpen((prev) => ({ ...prev, [entryId]: true }));
		setCurrentExtraFieldsEntry(entry);
	};

	// Close extra fields modal
	const closeExtraFieldsModal = (entryId: string) => {
		setExtraFieldsModalOpen((prev) => ({ ...prev, [entryId]: false }));
		setCurrentExtraFieldsEntry(null);
	};

	// Copy text to clipboard
	const copyToClipboard = (
		text: string,
		uniqueKey: string,
		successMessage: string,
	) => {
		if (!text) return;
		navigator.clipboard.writeText(text).then(
			() => {
				toast.success(successMessage);
				setCopiedStatus((prev) => ({ ...prev, [uniqueKey]: true }));
				setTimeout(
					() => setCopiedStatus((prev) => ({ ...prev, [uniqueKey]: false })),
					1500,
				);
			},
			(err) => {
				console.error("Fehler beim Kopieren:", err);
				toast.error("Fehler beim Kopieren: " + (err.message || err));
			},
		);
	};

	// Handle delete entry
	const handleDelete = async (id: Id<"kunden_dokumentation">) => {
		if (window.confirm("Möchten Sie diesen Eintrag wirklich löschen?")) {
			try {
				await removeEintrag({ id });
				toast.success("Eintrag erfolgreich gelöscht.");
			} catch (error) {
				console.error("Fehler beim Löschen:", error);
				toast.error("Fehler beim Löschen: " + (error as Error).message);
			}
		}
	};

	if (eintraege.length === 0) {
		return (
			<div className="text-center py-6 text-sm text-gray-400">
				Keine Einträge in dieser Kategorie vorhanden.
			</div>
		);
	}

	// Get field types from category
	const fieldTypes: Record<string | number, string> = {};
	kategorie.felder.forEach((feld) => {
		if (feld.feldId !== undefined) {
			fieldTypes[feld.feldId] = feld.typ;
		}
		fieldTypes[feld.name] = feld.typ; // Also store by name for backward compatibility
	});

	const formatFieldValue = (
		value: string,
		feld: KategorieFeld,
		entryIdStr: string,
	) => {
		if (!value && feld.typ !== FIELD_TYPES.CHECKBOX)
			return <span className="text-xs text-gray-500">-</span>;

		const visibilityKey = `${entryIdStr}-${feld.feldId}`;
		const copyKey = `${entryIdStr}-${feld.feldId}`;
		const isCopied = copiedStatus[copyKey];

		switch (feld.typ) {
			case FIELD_TYPES.PASSWORD:
				const isVisible = passwordVisibility[visibilityKey];
				return (
					<div className="flex items-center gap-1 min-w-[150px]">
						<span className="flex-grow font-mono text-xs">
							{isVisible ? value : "••••••••"}
						</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => togglePasswordVisibility(entryIdStr, feld.feldId)}
							className="h-5 w-5 text-gray-400 hover:text-gray-300"
						>
							{isVisible ? (
								<EyeOff className="h-3 w-3" />
							) : (
								<Eye className="h-3 w-3" />
							)}
						</Button>
						{feld.istKopierbar !== false && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									copyToClipboard(value, copyKey, "Passwort kopiert")
								}
								className={`h-5 w-5 text-gray-400 hover:text-gray-300 ${isCopied ? "text-green-400" : ""}`}
							>
								{isCopied ? (
									<CheckCircle className="h-3 w-3" />
								) : (
									<Copy className="h-3 w-3" />
								)}
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.URL:
				return (
					<div className="flex items-center gap-1">
						<a
							href={value.startsWith("http") ? value : `//${value}`}
							target="_blank"
							rel="noopener noreferrer"
							className="text-xs text-blue-400 hover:underline truncate"
							title={value}
						>
							{value}
						</a>
						<Button
							variant="ghost"
							size="icon"
							onClick={() =>
								window.open(
									value.startsWith("http") ? value : `//${value}`,
									"_blank",
								)
							}
							className="h-5 w-5 text-gray-400 hover:text-gray-300"
						>
							<ExternalLink className="h-3 w-3" />
						</Button>
						{feld.istKopierbar !== false && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() => copyToClipboard(value, copyKey, "URL kopiert")}
								className={`h-5 w-5 text-gray-400 hover:text-gray-300 ${isCopied ? "text-green-400" : ""}`}
							>
								{isCopied ? (
									<CheckCircle className="h-3 w-3" />
								) : (
									<Copy className="h-3 w-3" />
								)}
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.EMAIL:
				return (
					<div className="flex items-center gap-1">
						<a
							href={`mailto:${value}`}
							className="text-xs text-blue-400 hover:underline truncate"
							title={value}
						>
							{value}
						</a>
						{feld.istKopierbar !== false && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									copyToClipboard(value, copyKey, "E-Mail kopiert")
								}
								className={`h-5 w-5 text-gray-400 hover:text-gray-300 ${isCopied ? "text-green-400" : ""}`}
							>
								{isCopied ? (
									<CheckCircle className="h-3 w-3" />
								) : (
									<Copy className="h-3 w-3" />
								)}
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.TEXTAREA:
				const snippet =
					value.length > 50 ? value.substring(0, 47) + "..." : value;
				return (
					<div className="flex items-center gap-1 text-xs">
						<span className="truncate" title={value}>
							{snippet}
						</span>
						{value && feld.istKopierbar !== false && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									copyToClipboard(value, copyKey, `${feld.name} kopiert`)
								}
								className={`h-5 w-5 text-gray-400 hover:text-gray-300 ${isCopied ? "text-green-400" : ""}`}
							>
								{isCopied ? (
									<CheckCircle className="h-3 w-3" />
								) : (
									<Copy className="h-3 w-3" />
								)}
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.CHECKBOX:
				return value === "true" ? (
					<span className="text-xs text-green-400">Ja</span>
				) : (
					<span className="text-xs text-red-400">Nein</span>
				);
			default:
				return (
					<div className="flex items-center gap-1 text-xs">
						<span className="truncate" title={value}>
							{value}
						</span>
						{value && feld.istKopierbar !== false && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() =>
									copyToClipboard(value, copyKey, `${feld.name} kopiert`)
								}
								className={`h-5 w-5 text-gray-400 hover:text-gray-300 ${isCopied ? "text-green-400" : ""}`}
							>
								{isCopied ? (
									<CheckCircle className="h-3 w-3" />
								) : (
									<Copy className="h-3 w-3" />
								)}
							</Button>
						)}
					</div>
				);
		}
	};

	return (
		<div className="space-y-3">
			{/* Extra Fields Modal */}
			{currentExtraFieldsEntry && (
				<Dialog
					open={Object.values(extraFieldsModalOpen).some(Boolean)}
					onOpenChange={() =>
						closeExtraFieldsModal(currentExtraFieldsEntry._id.toString())
					}
				>
					<DialogContent
						className="sm:max-w-md"
						aria-describedby="extrafelder-description"
					>
						<DialogHeader>
							<DialogTitle>Extrafelder</DialogTitle>
							<div
								id="extrafelder-description"
								className="text-sm text-gray-400"
							>
								Zusätzliche Informationen zu diesem Eintrag
							</div>
							<DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
								<X className="h-4 w-4" />
								<span className="sr-only">Schließen</span>
							</DialogClose>
						</DialogHeader>
						<div className="grid grid-cols-1 gap-4 py-4">
							{kategorie.felder
								.filter((feld) => feld.istExtrafeld)
								.map((feld) => {
									const feldwertEintrag =
										currentExtraFieldsEntry.feldwerte.find(
											(fw) => fw.feldId === feld.feldId,
										);
									const value = feldwertEintrag?.feldWert || "";
									if (!value && feld.typ !== FIELD_TYPES.CHECKBOX) return null;

									return (
										<div key={feld.feldId} className="space-y-1">
											<h3 className="text-sm font-medium">
												{feld.name}
												{feld.istErforderlich ? " *" : ""}
											</h3>
											<div className="bg-gray-800/60 p-2 rounded-md">
												{formatFieldValue(
													value,
													feld,
													currentExtraFieldsEntry._id.toString(),
												)}
											</div>
										</div>
									);
								})}
						</div>
					</DialogContent>
				</Dialog>
			)}

			{eintraege.map((eintrag) => (
				<Card
					key={eintrag._id}
					className="bg-gray-800/40 border border-gray-700/50 shadow-sm hover:shadow-md transition-shadow duration-200"
				>
					<CardContent className="p-3">
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-3 gap-y-1.5 mb-2">
							{kategorie.felder
								.filter((feld) => !feld.istExtrafeld) // Filter out extra fields
								.map((feld) => {
									const feldwertEintrag = eintrag.feldwerte.find(
										(fw) => fw.feldId === feld.feldId,
									);
									return (
										<div key={feld.feldId} className="py-0.5 break-words">
											<div className="text-[11px] text-gray-400/80 mb-0.5">
												{feld.name}
												{feld.istErforderlich ? " *" : ""}
											</div>
											<div className="text-gray-200">
												{formatFieldValue(
													feldwertEintrag?.feldWert || "",
													feld,
													eintrag._id.toString(),
												)}
											</div>
										</div>
									);
								})}
						</div>
						{/* Notizen field is now handled like any other field and can be marked as istExtrafeld */}
						<div className="flex justify-end gap-1 mt-2 pt-1.5 border-t border-gray-700/40">
							{/* Extra Fields Button - Only show if there are extra fields with values */}
							{kategorie.felder.some((feld) => feld.istExtrafeld) &&
								eintrag.feldwerte.some((fw) => {
									const extraField = kategorie.felder.find(
										(f) => f.feldId === fw.feldId && f.istExtrafeld,
									);
									return extraField && fw.feldWert;
								}) && (
									<Button
										variant="ghost"
										size="sm"
										onClick={() =>
											openExtraFieldsModal(eintrag._id.toString(), eintrag)
										}
										className="h-6 px-1.5 text-purple-400 hover:text-purple-300 hover:bg-purple-500/10 gap-0.5 text-xs"
									>
										<MoreHorizontal className="h-2.5 w-2.5" /> Extrafelder
									</Button>
								)}
							<Button
								variant="ghost"
								size="sm"
								onClick={() => onEdit(eintrag._id)}
								className="h-6 px-1.5 text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 gap-0.5 text-xs"
							>
								<Pencil className="h-2.5 w-2.5" /> Bearbeiten
							</Button>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => handleDelete(eintrag._id)}
								className="h-6 px-1.5 text-red-400 hover:text-red-300 hover:bg-red-500/10 gap-0.5 text-xs"
							>
								<Trash2 className="h-2.5 w-2.5" /> Löschen
							</Button>
						</div>
					</CardContent>
				</Card>
			))}
		</div>
	);
}
