import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import { Button } from "@/components/_shared/Button";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { useMutation, useQuery } from "convex/react";
import { Check, Copy, Eye, EyeOff, NotebookText, Pencil, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface DokuEntryFormProps {
	kundenId: Id<"kunden">;
	kategorieId: Id<"system_doku_kategorien">; // For backward compatibility
	editingId: Id<"kunden_dokumentation"> | null;
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

export function DokuEntryForm({
	kundenId,
	kategorieId,
	editingId,
	onSubmitSuccess,
	onCancel,
}: DokuEntryFormProps) {
	const kategorie = useQuery(api.system.dokuKategorien.get, {
		id: kategorieId,
	});
	const editingEintrag = editingId
		? useQuery(api.kunden.dokumentation.get, { id: editingId })
		: null;

	const createEintrag = useMutation(api.kunden.dokumentation.create);
	const updateEintrag = useMutation(api.kunden.dokumentation.update);

	const [formState, setFormState] = useState<{
		feldwerte: { feldId: number; feldWert: string }[];
	}>({
		feldwerte: [],
	});

	const [fieldVisibility, setFieldVisibility] = useState<
		Record<string, boolean>
	>({});

	// Initialize form state when category or editing entry changes
	useEffect(() => {
		if (kategorie) {
			// Initialize field values from category fields
			const initialFeldwerte = kategorie.felder.map((feld) => ({
				feldId: feld.feldId,
				feldWert: "", // Default to empty string
			}));

			// Initialize field visibility based on istVersteckt property
			const initialVisibility: Record<string, boolean> = {};
			kategorie.felder.forEach((feld) => {
				// For password fields, always start hidden
				if (feld.typ === FIELD_TYPES.PASSWORD) {
					initialVisibility[feld.name] = false;
				}
				// For fields marked as hidden, start hidden unless editing
				else if (feld.istVersteckt) {
					initialVisibility[feld.name] = editingEintrag ? true : false;
				}
				// For all other fields, start visible
				else {
					initialVisibility[feld.name] = true;
				}
			});
			setFieldVisibility(initialVisibility);

			// If editing, populate with existing values
			if (editingEintrag) {
				const editingFeldwerte = [...initialFeldwerte];

				// Handle both feldId and feldName for backward compatibility
				editingEintrag.feldwerte.forEach((fw: any) => {
					let index = -1;
					let feldDefinition = null;

					// Try to find by feldId first
					if (fw.feldId !== undefined) {
						index = editingFeldwerte.findIndex((f) => f.feldId === fw.feldId);
						feldDefinition = kategorie.felder.find(
							(f) => f.feldId === fw.feldId,
						);
					}

					// If not found and we have feldName, try to find by name
					if (index === -1 && fw.feldName !== undefined) {
						const matchingFeld = kategorie.felder.find(
							(f) => f.name === fw.feldName,
						);
						if (matchingFeld) {
							index = editingFeldwerte.findIndex(
								(f) => f.feldId === matchingFeld.feldId,
							);
							feldDefinition = matchingFeld;
						}
					}

					if (index !== -1 && feldDefinition) {
						// For SELECT fields, check if the stored value is still a valid option
						if (
							feldDefinition.typ === FIELD_TYPES.SELECT &&
							feldDefinition.optionen
						) {
							if (feldDefinition.optionen.includes(fw.feldWert)) {
								editingFeldwerte[index].feldWert = fw.feldWert;
							} else {
								editingFeldwerte[index].feldWert = ""; // Reset if option no longer valid
								toast.info(
									`Die vorherige Auswahl für "${feldDefinition.name}" ist nicht mehr gültig und wurde zurückgesetzt.`,
								);
							}
						} else {
							editingFeldwerte[index].feldWert = fw.feldWert;
						}
					}
				});

				setFormState({
					feldwerte: editingFeldwerte,
				});
			} else {
				setFormState({
					feldwerte: initialFeldwerte,
				});
			}
		}
	}, [kategorie, editingEintrag]);

	// Toggle field visibility
	const toggleFieldVisibility = (fieldName: string) => {
		setFieldVisibility((prev) => ({
			...prev,
			[fieldName]: !prev[fieldName],
		}));
	};

	// Handle input change
	const handleInputChange = (fieldId: number, value: string | boolean) => {
		const newFeldwerte = [...formState.feldwerte];
		const index = newFeldwerte.findIndex((f) => f.feldId === fieldId);

		if (index !== -1) {
			newFeldwerte[index].feldWert =
				typeof value === "boolean" ? value.toString() : value;

			setFormState({
				...formState,
				feldwerte: newFeldwerte,
			});
		}
	};

	// Handle form submission
	const handleSubmit = async () => {
		// Add a check for kategorie before accessing its properties
		if (!kategorie) {
			toast.error(
				"Kategorie-Daten sind nicht geladen. Bitte versuchen Sie es erneut.",
			);
			return;
		}

		try {
			if (editingId) {
				await updateEintrag({
					id: editingId,
					feldwerte: formState.feldwerte,
				});
			} else {
				// Get the kategorieID from the kategorie object
				await createEintrag({
					kundenId,
					kategorieID: kategorie.kategorieID,
					feldwerte: formState.feldwerte,
				});
			}
			onSubmitSuccess();
		} catch (error) {
			console.error("Fehler beim Speichern:", error);
			toast.error("Fehler beim Speichern: " + (error as Error).message);
		}
	};

	if (!kategorie) {
		return <div className="text-center py-8">Kategorie wird geladen...</div>;
	}

	// Render field input based on type
	const renderFieldInput = (feld: {
		feldId: number;
		name: string;
		typ: string;
		istErforderlich: boolean;
		optionen?: string[];
		placeholder?: string;
		istKopierbar?: boolean;
		istVersteckt?: boolean;
	}) => {
		const feldwert = formState.feldwerte.find((f) => f.feldId === feld.feldId);
		const value = feldwert?.feldWert || "";
		const fieldPlaceholder = feld.placeholder || `${feld.name} eingeben...`;

		switch (feld.typ) {
			case FIELD_TYPES.TEXTAREA:
				return (
					<Textarea
						id={`field-${feld.feldId}`}
						value={value}
						onChange={(e) => handleInputChange(feld.feldId, e.target.value)}
						required={feld.istErforderlich}
						placeholder={fieldPlaceholder}
						className="min-h-[100px] bg-gray-800/60 border-gray-700"
					/>
				);
			case FIELD_TYPES.PASSWORD:
				return (
					<div className="relative">
						<Input
							id={`field-${feld.feldId}`}
							type={fieldVisibility[feld.name] ? "text" : "password"}
							value={value}
							onChange={(e) => handleInputChange(feld.feldId, e.target.value)}
							required={feld.istErforderlich}
							placeholder={fieldPlaceholder}
							className={`${feld.istKopierbar ? "pr-20" : "pr-10"} bg-gray-800/60 border-gray-700`}
						/>
						<div className="absolute right-1 top-1 flex">
							{feld.istKopierbar && (
								<Button
									type="button"
									variant="ghost"
									size="icon"
									className="h-8 w-8 text-gray-400"
									onClick={() => {
										navigator.clipboard.writeText(value);
										toast.success("Wert in die Zwischenablage kopiert");
									}}
								>
									<Copy className="h-4 w-4" />
								</Button>
							)}
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="h-8 w-8 text-gray-400"
								onClick={() => toggleFieldVisibility(feld.name)}
							>
								{fieldVisibility[feld.name] ? (
									<EyeOff className="h-4 w-4" />
								) : (
									<Eye className="h-4 w-4" />
								)}
							</Button>
						</div>
					</div>
				);
			case FIELD_TYPES.CHECKBOX:
				return (
					<Checkbox
						id={`field-${feld.feldId}`}
						checked={value === "true"}
						onCheckedChange={(checked) =>
							handleInputChange(feld.feldId, !!checked)
						}
					/>
				);
			case FIELD_TYPES.SELECT:
				return (
					<Select
						value={value}
						onValueChange={(value) => handleInputChange(feld.feldId, value)}
					>
						<SelectTrigger className="bg-gray-800/60 border-gray-700">
							<SelectValue placeholder={fieldPlaceholder} />
						</SelectTrigger>
						<SelectContent>
							{feld.optionen?.map((option) => (
								<SelectItem key={option} value={option}>
									{option}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				);
			default:
				return (
					<div className="relative">
						<Input
							id={`field-${feld.feldId}`}
							type={feld.typ === FIELD_TYPES.NUMBER ? "number" : feld.typ}
							value={value}
							onChange={(e) => handleInputChange(feld.feldId, e.target.value)}
							required={feld.istErforderlich}
							placeholder={fieldPlaceholder}
							className={`${feld.istKopierbar ? "pr-10" : ""} bg-gray-800/60 border-gray-700`}
						/>
						{feld.istKopierbar && value && (
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-1 top-1 h-8 w-8 text-gray-400"
								onClick={() => {
									navigator.clipboard.writeText(value);
									toast.success("Wert in die Zwischenablage kopiert");
								}}
							>
								<Copy className="h-4 w-4" />
							</Button>
						)}
					</div>
				);
		}
	};

	return (
		<ModalFormDialog
			isOpen={true}
			onClose={onCancel}
			title={editingId ? `${kategorie.name} bearbeiten` : `Neuer ${kategorie.name} Eintrag`}
			icon={editingId ? <Pencil className="h-3.5 w-3.5" /> : <NotebookText className="h-3.5 w-3.5" />}
			footerAction={{
				label: editingId ? "Aktualisieren" : "Speichern",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />
			}}
			maxWidth="2xl"
		>
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{kategorie.felder.map((feld) => (
					<div key={feld.feldId} className="space-y-2">
						<Label
							htmlFor={`field-${feld.feldId}`}
							className="text-sm flex items-center gap-1"
						>
							{feld.name}
							{feld.istErforderlich && (
								<span className="text-red-500">*</span>
							)}
						</Label>
						{renderFieldInput(feld)}
					</div>
				))}
			</div>
		</ModalFormDialog>
	);
}
