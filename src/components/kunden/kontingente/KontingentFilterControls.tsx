import type { Doc } from "@/../convex/_generated/dataModel";
import {
	type FilterConfig,
	type FilterOption,
	GenericFilterControls,
} from "@/components/layout/GenericFilterControls";
import React from "react";

// Typen aus der Hauptdatei übernehmen oder hier neu definieren
interface KontingentFilter {
	kundeId: string;
	zeitraum: "all" | "active" | "inactive" | "upcoming" | "expired" | "custom";
	startDatum: string;
	endDatum: string;
}
type KundeDoc = Doc<"kunden">;

interface KontingentFilterControlsProps {
	filter: KontingentFilter;
	searchTerm: string;
	kunden: KundeDoc[];
	onFilterChange: (field: keyof KontingentFilter, value: string) => void;
	onSearchTermChange: (value: string) => void;
	onResetAllFilters?: () => void;
}

export function KontingentFilterControls({
	filter,
	searchTerm,
	kunden,
	onFilterChange,
	onSearchTermChange,
	onResetAllFilters,
}: KontingentFilterControlsProps) {
	const kundenOptions: FilterOption[] = [
		{ value: "__ALL__", label: "Alle Kunden" },
		...kunden.map((kunde) => ({
			value: kunde._id,
			label: kunde.name,
		})),
	];

	const zeitraumStatusOptions: FilterOption[] = [
		{ value: "all", label: "Alle Status" },
		{ value: "active", label: "Aktiv" },
		{ value: "inactive", label: "Inaktiv" },
		{ value: "upcoming", label: "Zukünftig" },
		{ value: "expired", label: "Abgelaufen" },
		{ value: "custom", label: "Zeitraum wählen..." },
	];

	const filtersConfig: FilterConfig[] = [
		{
			type: "select",
			id: "kontingentKundeFilter",
			value: filter.kundeId || "__ALL__",
			onChange: (value) =>
				onFilterChange("kundeId", value === "__ALL__" ? "" : value),
			options: kundenOptions,
			placeholder: "Alle Kunden",
			triggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			minWidth: "150px",
		},
		{
			type: "dateRange", // Using dateRange for status/zeitraum combined with custom dates
			idPrefix: "kontingentZeitraumStatus",
			zeitraumValue: filter.zeitraum,
			startDateValue: filter.startDatum,
			endDateValue: filter.endDatum,
			onZeitraumChange: (value) => onFilterChange("zeitraum", value),
			onStartDateChange: (value) => onFilterChange("startDatum", value),
			onEndDateChange: (value) => onFilterChange("endDatum", value),
			zeitraumOptions: zeitraumStatusOptions,
			selectTriggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			customDateInputClassName:
				"h-8 text-xs w-auto bg-gray-800 border-gray-700",
			minWidth: "130px",
		},
		{
			type: "search",
			id: "kontingentSearch",
			value: searchTerm,
			onChange: onSearchTermChange,
			placeholder: "Suchen (Kunde, Name)...",
			className: "flex-grow",
			inputClassName: "h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700",
		},
	];

	return (
		<GenericFilterControls
			filters={filtersConfig}
			onResetAll={onResetAllFilters}
		/>
	);
}
