import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/_shared/Card";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Euro, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

interface StandortData {
	strasse: string;
	plz: string;
	ort: string;
	land?: string;
	istHauptstandort: boolean;
}

interface AnsprechpartnerData {
	name: string;
	email?: string;
	telefon?: string;
	mobil?: string;
	position?: string;
	istHauptansprechpartner: boolean;
	istEmailLieferscheinEmpfaenger?: boolean;
	istEmailUebersichtEmpfaenger?: boolean;
	istEmailAnrede?: boolean;
}

interface KundeFormData {
	name: string;
	stundensatz: string;
	anfahrtskosten: string;
	standorte: StandortData[];
	ansprechpartner: AnsprechpartnerData[];
}

interface KundenPageFormProps {
	initialData?: Doc<"kunden">;
	onSubmit: (data: any) => void;
	isSubmitting: boolean;
	formId: string;
}

const defaultStandort: StandortData = {
	strasse: "",
	plz: "",
	ort: "",
	land: "Deutschland",
	istHauptstandort: false,
};

const defaultAnsprechpartner: AnsprechpartnerData = {
	name: "",
	email: "",
	telefon: "",
	mobil: "",
	position: "",
	istHauptansprechpartner: false,
	istEmailLieferscheinEmpfaenger: false,
	istEmailUebersichtEmpfaenger: false,
	istEmailAnrede: false,
};

export function KundenPageForm({ 
	initialData, 
	onSubmit, 
	isSubmitting, 
	formId 
}: KundenPageFormProps) {
	const [formState, setFormState] = useState<KundeFormData>({
		name: "",
		stundensatz: "",
		anfahrtskosten: "",
		standorte: [{ ...defaultStandort, istHauptstandort: true }],
		ansprechpartner: [{ ...defaultAnsprechpartner, istHauptansprechpartner: true }],
	});

	useEffect(() => {
		if (initialData) {
			setFormState({
				name: initialData.name,
				stundensatz: initialData.stundenpreis?.toString() || "",
				anfahrtskosten: initialData.anfahrtskosten?.toString() || "",
				standorte: initialData.standorte?.length ? initialData.standorte : [{ ...defaultStandort, istHauptstandort: true }],
				ansprechpartner: initialData.ansprechpartner?.length ? initialData.ansprechpartner : [{ ...defaultAnsprechpartner, istHauptansprechpartner: true }],
			});
		}
	}, [initialData]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
		arrayName?: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">,
		index?: number,
	) => {
		const { name, value, type, checked } = e.target;
		const propValue = type === "checkbox" ? checked : value;

		if (arrayName && index !== undefined) {
			setFormState((prev) => {
				const newArray = [...prev[arrayName]];
				(newArray[index] as any)[name] = propValue;

				// Handle primary toggling
				if (name === "istHauptstandort" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index) (item as StandortData).istHauptstandort = false;
					});
				}
				if (name === "istHauptansprechpartner" && checked) {
					newArray.forEach((item, i) => {
						if (i !== index)
							(item as AnsprechpartnerData).istHauptansprechpartner = false;
					});
				}
				return { ...prev, [arrayName]: newArray };
			});
		} else {
			setFormState((prev) => ({ ...prev, [name]: propValue }));
		}
	};

	const addToArray = (arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">) => {
		setFormState((prev) => ({
			...prev,
			[arrayName]: [
				...prev[arrayName],
				arrayName === "standorte" ? { ...defaultStandort } : { ...defaultAnsprechpartner },
			],
		}));
	};

	const removeFromArray = (arrayName: keyof Pick<KundeFormData, "standorte" | "ansprechpartner">, index: number) => {
		setFormState((prev) => ({
			...prev,
			[arrayName]: prev[arrayName].filter((_, i) => i !== index),
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		
		// Validate E-Mail-Anrede (only one allowed)
		const emailAnredeCount = formState.ansprechpartner.filter(a => a.istEmailAnrede).length;
		if (emailAnredeCount > 1) {
			alert("Nur ein Ansprechpartner kann als E-Mail-Anrede ausgewählt werden.");
			return;
		}
		
		const { stundensatz, ...restFormState } = formState;
		const processedData = {
			...restFormState,
			stundenpreis: stundensatz ? parseFloat(stundensatz) : 0,
			anfahrtskosten: formState.anfahrtskosten ? parseFloat(formState.anfahrtskosten) : 0,
			ansprechpartner: formState.ansprechpartner.map((a) => ({
				...a,
				email: a.email || undefined,
				telefon: a.telefon || undefined,
				mobil: a.mobil || undefined,
				position: a.position || undefined,
				istEmailAnrede: a.istEmailAnrede || false,
				istEmailLieferscheinEmpfaenger: a.istEmailLieferscheinEmpfaenger || false,
				istEmailUebersichtEmpfaenger: a.istEmailUebersichtEmpfaenger || false,
			})),
		};

		onSubmit(processedData);
	};

	return (
		<form id={formId} onSubmit={handleSubmit} className="space-y-3">
			{/* Basic Information */}
			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-base">Grunddaten</CardTitle>
				</CardHeader>
				<CardContent className="space-y-2 pt-2">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
						<div>
							<Label htmlFor="name" className="text-sm">Firmenname *</Label>
							<Input
								id="name"
								name="name"
								value={formState.name}
								onChange={handleInputChange}
								required
								placeholder="Firmenname"
								className="h-8 text-sm"
							/>
						</div>
						<div>
							<Label htmlFor="stundensatz" className="text-sm">Stundensatz €</Label>
							<Input
								id="stundensatz"
								name="stundensatz"
								type="number"
								step="0.01"
								value={formState.stundensatz}
								onChange={handleInputChange}
								placeholder="0.00"
								className="h-8 text-sm"
							/>
						</div>
						<div>
							<Label htmlFor="anfahrtskosten" className="text-sm">Anfahrtskosten €</Label>
							<Input
								id="anfahrtskosten"
								name="anfahrtskosten"
								type="number"
								step="0.01"
								value={formState.anfahrtskosten}
								onChange={handleInputChange}
								placeholder="0.00"
								className="h-8 text-sm"
							/>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Standorte */}
			<Card>
				<CardHeader className="pb-2">
					<div className="flex items-center justify-between">
						<CardTitle className="text-base">Standorte</CardTitle>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("standorte")}
							className="h-7 text-xs"
						>
							<Plus className="h-3 w-3 mr-1" />
							Standort hinzufügen
						</Button>
					</div>
				</CardHeader>
				<CardContent className="space-y-2 pt-2">
					{formState.standorte.map((standort, index) => (
						<div key={index} className="border border-gray-700 rounded-lg p-4 space-y-3">
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-2">
									<Checkbox
										id={`standort-${index}-haupt`}
										name="istHauptstandort"
										checked={standort.istHauptstandort}
										onCheckedChange={(checked) => handleInputChange(
											{ target: { name: 'istHauptstandort', type: 'checkbox', checked } } as any,
											"standorte",
											index
										)}
									/>
									<Label htmlFor={`standort-${index}-haupt`} className="text-sm font-medium text-blue-400">
										Hauptstandort
									</Label>
								</div>
								
								{formState.standorte.length > 1 && (
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() => removeFromArray("standorte", index)}
										className="text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>
							
							<div>
								<Label htmlFor={`standort-${index}-strasse`}>Straße *</Label>
								<Input
									id={`standort-${index}-strasse`}
									name="strasse"
									value={standort.strasse}
									onChange={(e) => handleInputChange(e, "standorte", index)}
									placeholder="Straße und Hausnummer"
								/>
							</div>
							
							<div className="grid grid-cols-1 md:grid-cols-4 gap-3">
								<div>
									<Label htmlFor={`standort-${index}-plz`}>PLZ</Label>
									<Input
										id={`standort-${index}-plz`}
										name="plz"
										value={standort.plz}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="PLZ"
									/>
								</div>
								<div className="md:col-span-2">
									<Label htmlFor={`standort-${index}-ort`}>Ort</Label>
									<Input
										id={`standort-${index}-ort`}
										name="ort"
										value={standort.ort}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="Ort"
									/>
								</div>
								<div>
									<Label htmlFor={`standort-${index}-land`}>Land</Label>
									<Input
										id={`standort-${index}-land`}
										name="land"
										value={standort.land || ""}
										onChange={(e) => handleInputChange(e, "standorte", index)}
										placeholder="Land"
									/>
								</div>
							</div>
						</div>
					))}
				</CardContent>
			</Card>

			{/* Ansprechpartner */}
			<Card>
				<CardHeader className="pb-2">
					<div className="flex items-center justify-between">
						<CardTitle className="text-base">Ansprechpartner</CardTitle>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addToArray("ansprechpartner")}
							className="h-7 text-xs"
						>
							<Plus className="h-3 w-3 mr-1" />
							Ansprechpartner hinzufügen
						</Button>
					</div>
				</CardHeader>
				<CardContent className="space-y-2 pt-2">
					{formState.ansprechpartner.map((partner, index) => (
						<div key={index} className="border border-gray-700 rounded p-3 space-y-2">
							{/* Header mit Hauptansprechpartner und Löschen-Button */}
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-2">
									<Checkbox
										id={`partner-${index}-haupt`}
										name="istHauptansprechpartner"
										checked={partner.istHauptansprechpartner}
										onCheckedChange={(checked) => handleInputChange(
											{ target: { name: 'istHauptansprechpartner', type: 'checkbox', checked } } as any,
											"ansprechpartner",
											index
										)}
									/>
									<Label htmlFor={`partner-${index}-haupt`} className="text-sm font-medium text-blue-400">
										Hauptansprechpartner
									</Label>
								</div>
								
								{formState.ansprechpartner.length > 1 && (
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() => removeFromArray("ansprechpartner", index)}
										className="text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>
							
							{/* Kontaktdaten */}
							<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
								<div>
									<Label htmlFor={`partner-${index}-name`}>Name *</Label>
									<Input
										id={`partner-${index}-name`}
										name="name"
										value={partner.name}
										onChange={(e) => handleInputChange(e, "ansprechpartner", index)}
										required
										placeholder="Vor- und Nachname"
									/>
								</div>
								<div>
									<Label htmlFor={`partner-${index}-position`}>Position</Label>
									<Input
										id={`partner-${index}-position`}
										name="position"
										value={partner.position || ""}
										onChange={(e) => handleInputChange(e, "ansprechpartner", index)}
										placeholder="Position/Abteilung"
									/>
								</div>
								<div>
									<Label htmlFor={`partner-${index}-email`}>E-Mail</Label>
									<Input
										id={`partner-${index}-email`}
										name="email"
										type="email"
										value={partner.email || ""}
										onChange={(e) => handleInputChange(e, "ansprechpartner", index)}
										placeholder="<EMAIL>"
									/>
								</div>
								<div>
									<Label htmlFor={`partner-${index}-telefon`}>Telefon</Label>
									<Input
										id={`partner-${index}-telefon`}
										name="telefon"
										type="tel"
										value={partner.telefon || ""}
										onChange={(e) => handleInputChange(e, "ansprechpartner", index)}
										placeholder="+49 123 456789"
									/>
								</div>
								<div>
									<Label htmlFor={`partner-${index}-mobil`}>Mobil</Label>
									<Input
										id={`partner-${index}-mobil`}
										name="mobil"
										type="tel"
										value={partner.mobil || ""}
										onChange={(e) => handleInputChange(e, "ansprechpartner", index)}
										placeholder="+49 123 456789"
									/>
								</div>
							</div>
							
							{/* E-Mail Einstellungen - Kompakt gruppiert */}
							<div className="bg-gray-800/30 rounded-lg p-3">
								<Label className="text-sm font-medium text-gray-300 mb-3 block">E-Mail Einstellungen</Label>
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
									<div className="flex items-center space-x-2">
										<Checkbox
											id={`partner-${index}-lieferschein`}
											name="istEmailLieferscheinEmpfaenger"
											checked={partner.istEmailLieferscheinEmpfaenger || false}
											onCheckedChange={(checked) => handleInputChange(
												{ target: { name: 'istEmailLieferscheinEmpfaenger', type: 'checkbox', checked } } as any,
												"ansprechpartner",
												index
											)}
										/>
										<Label htmlFor={`partner-${index}-lieferschein`} className="text-sm text-blue-400">
											Lieferschein-Empfänger
										</Label>
									</div>
									
									<div className="flex items-center space-x-2">
										<Checkbox
											id={`partner-${index}-uebersicht`}
											name="istEmailUebersichtEmpfaenger"
											checked={partner.istEmailUebersichtEmpfaenger || false}
											onCheckedChange={(checked) => handleInputChange(
												{ target: { name: 'istEmailUebersichtEmpfaenger', type: 'checkbox', checked } } as any,
												"ansprechpartner",
												index
											)}
										/>
										<Label htmlFor={`partner-${index}-uebersicht`} className="text-sm text-green-400">
											Übersicht-Empfänger
										</Label>
									</div>
									
									<div className="flex items-center space-x-2">
										<Checkbox
											id={`partner-${index}-anrede`}
											name="istEmailAnrede"
											checked={partner.istEmailAnrede || false}
											onCheckedChange={(checked) => {
												// Ensure only one E-Mail-Anrede is selected
												const updatedAnsprechpartner = formState.ansprechpartner.map((a, i) => ({
													...a,
													istEmailAnrede: i === index ? !!checked : false
												}));
												setFormState(prev => ({
													...prev,
													ansprechpartner: updatedAnsprechpartner
												}));
											}}
										/>
										<Label htmlFor={`partner-${index}-anrede`} className="text-sm text-purple-400">
											E-Mail-Anrede
										</Label>
									</div>
								</div>
							</div>
						</div>
					))}
				</CardContent>
			</Card>
		</form>
	);
}