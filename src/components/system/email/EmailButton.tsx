import { Button } from "@/components/_shared/Button";
import { Mail } from "lucide-react";
import { useState } from "react";
import { EmailDialog } from "./EmailDialog";
import { Id } from "@/../convex/_generated/dataModel";

interface EmailButtonProps {
  type: "lieferschein" | "uebersicht";
  lieferscheinId?: Id<"kunden_lieferscheine">;
  kundeId?: Id<"kunden">;
  zeitraum?: string;
  pdfBase64?: string;
  variant?: "default" | "secondary" | "destructive" | "outline" | "ghost" | "link";
  className?: string;
  size?: "default" | "sm" | "lg" | "icon";
  tooltipText?: string;
}

export function EmailButton({
  type,
  lieferscheinId,
  kundeId,
  zeitraum,
  pdfBase64,
  variant = "ghost",
  className = "h-8 w-8 text-gray-400 hover:text-blue-400",
  size = "icon",
  tooltipText = "Per E-Mail senden",
}: EmailButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        title={tooltipText}
        onClick={() => setIsDialogOpen(true)}
      >
        <Mail className="h-4 w-4" />
        <span className="sr-only">{tooltipText}</span>
      </Button>
      
      <EmailDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        type={type}
        lieferscheinId={lieferscheinId}
        kundeId={kundeId}
        zeitraum={zeitraum}
        pdfBase64={pdfBase64}
      />
    </>
  );
}