import { api } from "@/../convex/_generated/api";
import { useUser } from "@clerk/clerk-react";
import { useMutation } from "convex/react";
import { AlertCircle, BugIcon, Lightbulb, X } from "lucide-react";
import React, { useState } from "react";

interface FeedbackDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

export function FeedbackDialog({ isOpen, onClose }: FeedbackDialogProps) {
	const [feedbackText, setFeedbackText] = useState("");
	const [feedbackType, setFeedbackType] = useState<"feature" | "bug">(
		"feature",
	);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const createFeedback = useMutation(api.system.feedback.create);
	const { user } = useUser();

	// Wenn der Dialog nicht geöffnet ist, nichts rendern
	if (!isOpen) return null;

	const handleSubmit = async () => {
		if (!feedbackText.trim() || !user) return;

		setIsSubmitting(true);

		try {
			await createFeedback({
				userName:
					`${user.firstName || ""} ${user.lastName || ""}`.trim() ||
					user.primaryEmailAddress?.emailAddress ||
					"",
				text: feedbackText,
				type: feedbackType,
			});

			// Erfolgreich gespeichert
			setFeedbackText("");
			onClose();
		} catch (error) {
			console.error("Fehler beim Speichern des Feedbacks:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div
			className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/70 p-4 overflow-y-auto"
			style={{ position: "fixed", top: 0, left: 0, right: 0, bottom: 0 }}
		>
			<div className="w-full max-w-md rounded-lg bg-gray-800 shadow-xl border border-gray-700 animate-in fade-in zoom-in-95 duration-200 my-auto">
				<div className="flex items-center justify-between p-4 border-b border-gray-700">
					<h3 className="text-lg font-medium text-white flex items-center">
						<AlertCircle className="mr-2 h-5 w-5 text-blue-400" />
						Feedback geben
					</h3>
					<button
						onClick={onClose}
						className="rounded-full p-1 text-gray-400 hover:bg-gray-700 hover:text-white"
					>
						<X className="h-5 w-5" />
					</button>
				</div>

				<div className="p-4">
					{/* Feedback-Typ Auswahl */}
					<div className="mb-4 flex space-x-2">
						<button
							onClick={() => setFeedbackType("feature")}
							className={`flex-1 py-2 px-3 rounded-md flex items-center justify-center ${
								feedbackType === "feature"
									? "bg-green-500/20 text-green-400 border border-green-500/30"
									: "bg-gray-700 text-gray-300 hover:bg-gray-600"
							}`}
						>
							<Lightbulb className="mr-2 h-4 w-4" />
							Neue Funktion
						</button>
						<button
							onClick={() => setFeedbackType("bug")}
							className={`flex-1 py-2 px-3 rounded-md flex items-center justify-center ${
								feedbackType === "bug"
									? "bg-red-500/20 text-red-400 border border-red-500/30"
									: "bg-gray-700 text-gray-300 hover:bg-gray-600"
							}`}
						>
							<BugIcon className="mr-2 h-4 w-4" />
							Bug melden
						</button>
					</div>

					{/* Feedback-Text */}
					<div className="mb-4">
						<textarea
							value={feedbackText}
							onChange={(e) => setFeedbackText(e.target.value)}
							placeholder={
								feedbackType === "feature"
									? "Beschreiben Sie Ihre Idee für eine neue Funktion..."
									: "Beschreiben Sie den gefundenen Fehler..."
							}
							className="w-full h-32 px-3 py-2 text-sm rounded-md border border-gray-700 bg-gray-900 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
					</div>

					{/* Buttons */}
					<div className="flex justify-end space-x-2">
						<button
							onClick={onClose}
							className="px-4 py-2 rounded-md bg-gray-700 text-gray-300 hover:bg-gray-600 focus:outline-none"
							disabled={isSubmitting}
						>
							Abbrechen
						</button>
						<button
							onClick={handleSubmit}
							className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
							disabled={!feedbackText.trim() || isSubmitting}
						>
							{isSubmitting ? "Wird gesendet..." : "Absenden"}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
