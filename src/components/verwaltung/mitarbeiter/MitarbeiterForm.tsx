import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { useMutation } from "convex/react";
import { Check, Pencil, PlusCircle, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// Interface für Mitarbeiterdaten
interface Mitarbeiter {
	_id: Id<"mitarbeiter">;
	name: string;
	email: string;
}

interface MitarbeiterFormProps {
	editingMitarbeiter: Mitarbeiter | null;
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

export function MitarbeiterForm({
	editingMitarbeiter,
	onSubmitSuccess,
	onCancel,
}: MitarbeiterFormProps) {
	const createMitarbeiter = useMutation(api.verwaltung.mitarbeiter.create);
	const updateMitarbeiter = useMutation(api.verwaltung.mitarbeiter.update);

	const [formState, setFormState] = useState({ name: "", email: "" });

	useEffect(() => {
		if (editingMitarbeiter) {
			setFormState({
				name: editingMitarbeiter.name,
				email: editingMitarbeiter.email,
			});
		} else {
			setFormState({ name: "", email: "" });
		}
	}, [editingMitarbeiter]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { id, value } = e.target;
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	const handleSubmit = () => {
		const { name, email } = formState;

		if (!name || !email) {
			toast.error("Bitte Name und E-Mail angeben.");
			return;
		}

		const mutationArgs = { name, email };

		const promise = editingMitarbeiter
			? updateMitarbeiter({ id: editingMitarbeiter._id, ...mutationArgs })
			: createMitarbeiter(mutationArgs);

		promise
			.then(() => {
				toast.success(
					`Mitarbeiter erfolgreich ${editingMitarbeiter ? "aktualisiert" : "angelegt"}`,
				);
				onSubmitSuccess();
			})
			.catch((error: Error) => {
				toast.error(`Fehler beim Speichern: ${String(error.message || error)}`);
			});
	};

	return (
		<ModalFormDialog
			isOpen={true}
			onClose={onCancel}
			title={editingMitarbeiter ? "Mitarbeiter bearbeiten" : "Neuen Mitarbeiter anlegen"}
			icon={editingMitarbeiter ? <Pencil className="h-3.5 w-3.5" /> : <PlusCircle className="h-3.5 w-3.5" />}
			footerAction={{
				label: editingMitarbeiter ? "Aktualisieren" : "Speichern",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />
			}}
			maxWidth="xl"
		>
			<div className="space-y-5">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label
							htmlFor="name"
							className="text-xs text-gray-400 mb-1 block"
						>
							Name *
						</Label>
						<Input
							id="name"
							value={formState.name}
							onChange={handleInputChange}
							required
							placeholder="Vor- und Nachname"
							className="h-9 bg-gray-800/60 border-gray-700"
						/>
					</div>
					<div>
						<Label
							htmlFor="email"
							className="text-xs text-gray-400 mb-1 block"
						>
							E-Mail *
						</Label>
						<Input
							id="email"
							type="email"
							value={formState.email}
							onChange={handleInputChange}
							required
							placeholder="<EMAIL>"
							className="h-9 bg-gray-800/60 border-gray-700"
						/>
					</div>
				</div>
			</div>
		</ModalFormDialog>
	);
}
