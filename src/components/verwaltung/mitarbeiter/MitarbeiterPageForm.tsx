import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Mail, User } from "lucide-react";
import { useEffect, useState } from "react";

interface MitarbeiterFormData {
	name: string;
	email: string;
}

interface MitarbeiterPageFormProps {
	initialData?: Doc<"mitarbeiter">;
	onSubmit: (data: any) => void;
	isSubmitting: boolean;
	formId: string;
}

export function MitarbeiterPageForm({ 
	initialData, 
	onSubmit, 
	isSubmitting, 
	formId 
}: MitarbeiterPageFormProps) {
	const [formState, setFormState] = useState<MitarbeiterFormData>({
		name: "",
		email: "",
	});

	useEffect(() => {
		if (initialData) {
			setFormState({
				name: initialData.name,
				email: initialData.email,
			});
		}
	}, [initialData]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormState((prev) => ({ ...prev, [name]: value }));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit(formState);
	};

	return (
		<form id={formId} onSubmit={handleSubmit}>
			<Card>
				<CardHeader>
					<CardTitle>Mitarbeiterdaten</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="name">Name *</Label>
						<div className="relative">
							<Input
								id="name"
								name="name"
								value={formState.name}
								onChange={handleInputChange}
								required
								placeholder="Vor- und Nachname eingeben"
								className="pl-10"
							/>
							<User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						</div>
					</div>
					
					<div>
						<Label htmlFor="email">E-Mail *</Label>
						<div className="relative">
							<Input
								id="email"
								name="email"
								type="email"
								value={formState.email}
								onChange={handleInputChange}
								required
								placeholder="<EMAIL>"
								className="pl-10"
							/>
							<Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						</div>
					</div>
				</CardContent>
			</Card>
		</form>
	);
}