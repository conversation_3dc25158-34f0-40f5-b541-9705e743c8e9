@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;

		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;

		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;

		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;

		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;

		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;

		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;

		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;

		--radius: 0.5rem;
		/* Controls the roundness */
	}

	/* You can add light theme variables here if needed */
	/*
  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    // ... other light theme variables ...
  }
  */
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
		font-family:
			"Inter Variable",
			ui-sans-serif,
			system-ui,
			-apple-system,
			BlinkMacSystemFont,
			"Segoe UI",
			Roboto,
			"Helvetica Neue",
			Arial,
			"Noto Sans",
			sans-serif,
			"Apple Color Emoji",
			"Segoe UI Emoji",
			"Segoe UI Symbol",
			"Noto Color Emoji";
	}
}

/* Remove old custom classes if they are now handled by Shadcn/Tailwind */
/*
.accent-text {
  @apply text-slate-600;
}

.button {
  @apply bg-gradient-to-r bg-blue-500;
}

.input-field {
  @apply w-full px-3 py-2 rounded-md bg-transparent border-2 border-slate-200 focus:outline-none focus:border-blue-500 transition-colors;
}

.auth-button {
  @apply w-full py-2 rounded-md text-white font-medium button hover:opacity-90 transition-opacity;
}

.link-text {
  @apply text-blue-500 hover:underline cursor-pointer font-medium;
}
*/
