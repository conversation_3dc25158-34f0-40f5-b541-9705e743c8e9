import {
	endOfMonth,
	endOfQuarter,
	endOfYear,
	format,
	startOfMonth,
	startOfQuarter,
	startOfYear,
	subMonths,
	subQuarters,
	subYears,
} from "date-fns";

/**
 * Formatiert einen Zeitstempel als relative Zeit (z.B. "vor 5 Minuten")
 */
export function formatRelativeTime(timestamp: number): string {
	const now = Date.now();
	const diffInSeconds = Math.floor((now - timestamp) / 1000);

	// Weniger als eine Minute
	if (diffInSeconds < 60) {
		return "gerade eben";
	}

	// Weniger als eine Stunde
	if (diffInSeconds < 3600) {
		const minutes = Math.floor(diffInSeconds / 60);
		return `vor ${minutes} ${minutes === 1 ? "Minute" : "Minuten"}`;
	}

	// Weniger als ein Tag
	if (diffInSeconds < 86400) {
		const hours = Math.floor(diffInSeconds / 3600);
		return `vor ${hours} ${hours === 1 ? "Stunde" : "Stunden"}`;
	}

	// Weniger als eine Woche
	if (diffInSeconds < 604800) {
		const days = Math.floor(diffInSeconds / 86400);
		return `vor ${days} ${days === 1 ? "Tag" : "Tagen"}`;
	}

	// Weniger als ein Monat
	if (diffInSeconds < 2592000) {
		const weeks = Math.floor(diffInSeconds / 604800);
		return `vor ${weeks} ${weeks === 1 ? "Woche" : "Wochen"}`;
	}

	// Weniger als ein Jahr
	if (diffInSeconds < 31536000) {
		const months = Math.floor(diffInSeconds / 2592000);
		return `vor ${months} ${months === 1 ? "Monat" : "Monaten"}`;
	}

	// Mehr als ein Jahr
	const years = Math.floor(diffInSeconds / 31536000);
	return `vor ${years} ${years === 1 ? "Jahr" : "Jahren"}`;
}

/**
 * Berechnet Datumsbereich basierend auf vordefinierten Optionen
 * Verwendet date-fns für präzise Datumsberechnungen
 */
export type DateRangeOption =
	| "all"
	| "month"
	| "lastMonth"
	| "quarter"
	| "lastQuarter"
	| "year"
	| "lastYear"
	| "custom"
	| "thisMonth"
	| "thisQuarter"
	| "thisYear"
	| "last7"
	| "last30";

export interface DateRange {
	startDate: Date;
	endDate: Date;
}

export function calculateDateRange(option: DateRangeOption): DateRange {
	const today = new Date();

	switch (option) {
		case "month":
		case "thisMonth": {
			// Erster Tag des aktuellen Monats bis letzter Tag des aktuellen Monats
			return {
				startDate: startOfMonth(today),
				endDate: endOfMonth(today),
			};
		}
		case "lastMonth": {
			// Erster Tag des letzten Monats bis letzter Tag des letzten Monats
			const lastMonth = subMonths(today, 1);
			return {
				startDate: startOfMonth(lastMonth),
				endDate: endOfMonth(lastMonth),
			};
		}
		case "quarter":
		case "thisQuarter": {
			// Erster Tag des aktuellen Quartals bis letzter Tag des aktuellen Quartals
			return {
				startDate: startOfQuarter(today),
				endDate: endOfQuarter(today),
			};
		}
		case "lastQuarter": {
			// Erster Tag des letzten Quartals bis letzter Tag des letzten Quartals
			const lastQuarter = subQuarters(today, 1);
			return {
				startDate: startOfQuarter(lastQuarter),
				endDate: endOfQuarter(lastQuarter),
			};
		}
		case "year":
		case "thisYear": {
			// Erster Tag des aktuellen Jahres bis letzter Tag des aktuellen Jahres
			return {
				startDate: startOfYear(today),
				endDate: endOfYear(today),
			};
		}
		case "lastYear": {
			// Erster Tag des letzten Jahres bis letzter Tag des letzten Jahres
			const lastYear = subYears(today, 1);
			return {
				startDate: startOfYear(lastYear),
				endDate: endOfYear(lastYear),
			};
		}
		case "last7": {
			// Letzte 7 Tage
			const startDate = new Date();
			startDate.setDate(today.getDate() - 7);
			startDate.setHours(0, 0, 0, 0);
			return {
				startDate,
				endDate: today,
			};
		}
		case "last30": {
			// Letzte 30 Tage
			const startDate = new Date();
			startDate.setDate(today.getDate() - 30);
			startDate.setHours(0, 0, 0, 0);
			return {
				startDate,
				endDate: today,
			};
		}
		case "all":
		default: {
			// Für "all" oder unbekannte Optionen: sehr früher Start bis heute
			return {
				startDate: new Date(0), // 1970-01-01
				endDate: today,
			};
		}
	}
}

/**
 * Konvertiert ein Date-Objekt in ein ISO-Datums-String (YYYY-MM-DD)
 */
export function toISODateString(date: Date): string {
	return date.toISOString().split("T")[0];
}

/**
 * Formatiert ein Datum in deutsches Format (DD.MM.YYYY)
 */
export function formatDateDE(date: Date): string {
	return format(date, "dd.MM.yyyy");
}

/**
 * Formatiert einen Datumsbereich für die Anzeige im Format "DD.MM.YYYY - DD.MM.YYYY"
 * Stellt sicher, dass das Enddatum korrekt als letzter Tag des Zeitraums angezeigt wird
 */
export function formatDateRangeDE(
	startDateStr: string,
	endDateStr: string,
): string {
	// Für das Startdatum verwenden wir das normale Datum
	const startDate = new Date(startDateStr);

	// Für das Enddatum prüfen wir, ob es sich um einen Monatsende handelt
	// und korrigieren es gegebenenfalls
	const endDate = new Date(endDateStr);

	// The specific correction for 30th/31st day of month has been removed.
	// The function will now format the dates as they are provided.
	// If end-of-month logic is required, it should be applied when the endDateStr is generated.

	return `${formatDateDE(startDate)} - ${formatDateDE(endDate)}`;
}
