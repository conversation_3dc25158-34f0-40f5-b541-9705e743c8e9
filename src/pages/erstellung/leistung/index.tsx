import { api } from "@/../convex/_generated/api";
import type { Doc, Id } from "@/../convex/_generated/dataModel"; // Keep Id if used for types like KontingentOption
import { Button } from "@/components/_shared/Button";
import { CreateLieferscheinDialog } from "@/components/erstellung/leistung/CreateLieferscheinDialog";
// Removed unused imports: Card, Table components, Input, Select, Textarea, specific icons if not used by remaining JSX
// Removed useEffect, useMemo, useState as they are moved to the hook
// Removed toast as it's handled in the hook
import {
	LeistungDataTable,
	type LeistungMitNamen,
} from "@/components/erstellung/leistung/LeistungDataTable"; // Import type
import { LeistungFilterControls } from "@/components/erstellung/leistung/LeistungFilterControls";
import { LeistungForm } from "@/components/erstellung/leistung/LeistungForm";
import { EmptyState } from "@/components/layout/EmptyState"; // Import new EmptyState
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import {
	formatCurrency,
	formatDate, // Keep this import from formatUtils
	formatHours,
	formatTime,
} from "@/lib/utils/formatUtils";
import { useQuery } from "convex/react";
import { Clock, PlusCircle, TimerIcon, X } from "lucide-react"; // Added Clock
import { useLeistungenPageLogic } from "./hooks/useLeistungenPageLogic"; // Import the new hook

// KontingentOption might still be needed if CreateLieferscheinDialog or other components expect it
// or if it's part of data passed around. Review if it can be removed or moved.
// For now, keeping it if it's used by child components not directly refactored.
interface KontingentOption {
	_id: Id<"kunden_kontingente">;
	name: string;
	startDatum: number;
	stunden: number;
	verbrauchteStunden: number;
}

export function LeistungenPage() {
	const leistungenRaw = useQuery(api.erstellung.leistung.list) || [];
	const kundenRaw = useQuery(api.kunden.stammdaten.list) || [];
	const mitarbeiterRaw = useQuery(api.verwaltung.mitarbeiter.list) || [];
	// const kontingente = useQuery(api.kunden.kontingente.list) || []; // This is not passed to the hook, review if needed by LeistungForm directly

	const {
		editingId,
		showForm,
		setShowForm,
		searchTerm,
		filter,
		expandedDescriptions,
		showLieferscheinDialog,
		setShowLieferscheinDialog,
		selectedLeistungForLieferschein,
		initialFormData,
		handleFormSubmitSuccess,
		handleFormCancel,
		handleEdit,
		handleCreateLieferschein,
		handleDelete,
		handleFilterChange,
		handleSearchTermChange,
		resetFilters,
		toggleDescription,
		filteredLeistungen,
	} = useLeistungenPageLogic({
		leistungen: leistungenRaw as LeistungMitNamen[], // Cast as hook expects LeistungMitNamen
		kunden: kundenRaw as Doc<"kunden">[], // Cast based on hook's expectation
		mitarbeiter: mitarbeiterRaw as Doc<"mitarbeiter">[], // Cast based on hook's expectation
	});

	const actionButton = (
		<Button onClick={() => setShowForm(!showForm)} size="sm" className="gap-1">
			{showForm ? (
				<>
					<X className="h-4 w-4" /> Abbrechen
				</>
			) : (
				<>
					<PlusCircle className="h-4 w-4" /> Neue Leistung
				</>
			)}
		</Button>
	);

	const sumStunden = filteredLeistungen.reduce(
		(acc, curr) => acc + curr.stunden,
		0,
	);

	return (
		<PageLayout
			title="Leistungen"
			subtitle="Erfassen und verwalten Sie erbrachte Leistungen"
			action={actionButton}
		>
			{showForm && (
				<LeistungForm
					initialData={initialFormData}
					isEditing={!!editingId}
					kunden={kundenRaw as Doc<"kunden">[]} // Pass raw kunden data
					mitarbeiter={mitarbeiterRaw as Doc<"mitarbeiter">[]} // Pass raw mitarbeiter data
					onSubmitSuccess={handleFormSubmitSuccess}
					onCancel={handleFormCancel}
				/>
			)}

			<StandardDataTable
				title="Leistungsübersicht"
				infoSlot={
					<>
						<TimerIcon className="h-3.5 w-3.5 opacity-70" />
						<span>
							{filteredLeistungen.length} Einträge | {formatHours(sumStunden)}
						</span>
					</>
				}
				filterSlot={
					<LeistungFilterControls
						filter={filter}
						searchTerm={searchTerm}
						kunden={kundenRaw as Doc<"kunden">[]} // Pass raw kunden data
						onFilterChange={handleFilterChange}
						onSearchTermChange={handleSearchTermChange}
					/>
				}
			>
				{filteredLeistungen.length === 0 ? (
					<EmptyState
						icon={<Clock className="w-12 h-12" />} // Adjusted icon size for consistency
						title="Keine Leistungen gefunden"
						message={
							searchTerm || filter.kundeId || filter.zeitraum !== "last7"
								? "Versuchen Sie, Ihre Filterkriterien anzupassen"
								: "Erstellen Sie eine neue Leistung mit dem Button oben rechts"
						}
						actions={
							(searchTerm || filter.kundeId || filter.zeitraum !== "last7") && (
								<Button variant="outline" size="sm" onClick={resetFilters}>
									Filter zurücksetzen
								</Button>
							)
						}
					/>
				) : (
					<LeistungDataTable
						leistungen={filteredLeistungen}
						expandedDescriptions={expandedDescriptions}
						onEdit={handleEdit}
						onDelete={handleDelete}
						onToggleDescription={toggleDescription}
						onCreateLieferschein={handleCreateLieferschein}
						formatDate={formatDate} // Use imported formatDate
						formatTime={formatTime} // Use imported formatTime
						formatHours={formatHours} // Use imported formatHours
						formatCurrency={formatCurrency} // Use imported formatCurrency
					/>
				)}
			</StandardDataTable>

			{showLieferscheinDialog && selectedLeistungForLieferschein && (
				<CreateLieferscheinDialog
					isOpen={showLieferscheinDialog}
					onClose={() => setShowLieferscheinDialog(false)}
					leistung={selectedLeistungForLieferschein}
				/>
			)}
		</PageLayout>
	);
}

export default LeistungenPage;
