import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { LieferscheinEmptyState } from "@/components/erstellung/lieferscheine/LieferscheinEmptyState";
import { LieferscheinFilterControls } from "@/components/erstellung/lieferscheine/LieferscheinFilterControls";
import { LieferscheinRow } from "@/components/erstellung/lieferscheine/LieferscheinRow";
import { NewLieferscheinDialog } from "@/components/erstellung/lieferscheine/NewLieferscheinDialog";
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import { FileText, Plus } from "lucide-react";
import {
	LieferscheinFilter,
	useLieferscheinePageLogic,
} from "./hooks/useLieferscheinePageLogic";

export function LieferscheinePage() {
	const {
		isNewLieferscheinDialogOpen,
		setIsNewLieferscheinDialogOpen,
		searchTerm,
		setSearchTerm,
		filter,
		expandedRows,
		filteredLieferscheinGruppen,
		handleFilterChange,
		resetFilters,
		handleDelete,
		toggleExpandRow,
	} = useLieferscheinePageLogic();

	const actionButton = (
		<Button
			onClick={() => setIsNewLieferscheinDialogOpen(true)}
			size="sm"
			className="gap-1"
		>
			<Plus className="h-4 w-4" />
			Neuer Lieferschein
		</Button>
	);

	return (
		<PageLayout
			title="Lieferscheine"
			subtitle="Erstellen und verwalten Sie Lieferscheine für erbrachte Leistungen"
			action={actionButton}
		>
			<StandardDataTable
				title="Lieferscheinübersicht"
				infoSlot={
					<>
						<FileText className="h-3.5 w-3.5 opacity-70" />
						<span>
							{filteredLieferscheinGruppen.length}{" "}
							{filteredLieferscheinGruppen.length === 1
								? "Lieferschein"
								: "Lieferscheine"}
						</span>
					</>
				}
				filterSlot={
					<LieferscheinFilterControls
						filter={filter}
						searchTerm={searchTerm}
						onFilterChange={handleFilterChange}
						onSearchTermChange={setSearchTerm}
					/>
				}
			>
				<div className="overflow-x-auto">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="font-medium">Nummer</TableHead>
								<TableHead className="font-medium">Kunde</TableHead>
								<TableHead className="font-medium">Erstellt am</TableHead>
								<TableHead className="font-medium">Status</TableHead>
								<TableHead className="w-24 text-center">Aktionen</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredLieferscheinGruppen.length === 0 ? (
								<TableRow>
									<TableCell
										colSpan={5}
										className="text-center py-8 text-gray-400"
									>
										<LieferscheinEmptyState
											searchTerm={searchTerm}
											filterStatus={filter.status}
											filterZeitraum={filter.zeitraum}
											filterKundeId={filter.kundeId}
											onResetFilters={resetFilters}
										/>
									</TableCell>
								</TableRow>
							) : (
								filteredLieferscheinGruppen.map((gruppe) => (
									<LieferscheinRow
										key={gruppe.hauptLieferschein._id}
										lieferschein={gruppe.hauptLieferschein}
										korrekturen={gruppe.korrekturen}
										original={gruppe.original}
										onDelete={handleDelete}
										isExpanded={expandedRows.has(
											gruppe.hauptLieferschein._id.toString(),
										)}
										onToggleExpand={() =>
											toggleExpandRow(gruppe.hauptLieferschein._id.toString())
										}
									/>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</StandardDataTable>

			<NewLieferscheinDialog
				isOpen={isNewLieferscheinDialogOpen}
				onClose={() => setIsNewLieferscheinDialogOpen(false)}
			/>
		</PageLayout>
	);
}

export default LieferscheinePage;
