import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Label } from "@/components/_shared/Label";
import { PageLayout } from "@/components/layout/PageLayout";
import { formatDateRangeDE } from "@/lib/utils/dateUtils";
import {
	formatCurrency,
	formatDate,
	formatHours,
	formatTime,
} from "@/lib/utils/formatUtils";
import { PDFDownloadLink, PDFViewer, pdf } from "@react-pdf/renderer";
import { useQuery } from "convex/react";
import { Download, Eye, Mail } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

import KontingentList from "@/components/erstellung/uebersicht/KontingentList";
import LeistungList from "@/components/erstellung/uebersicht/LeistungList";
import { PDFDocumentComponent } from "@/components/erstellung/uebersicht/PDFDocument";
import SummarySection from "@/components/erstellung/uebersicht/SummarySection";
import UebersichtForm from "@/components/erstellung/uebersicht/UebersichtForm";
import { EmailDialog } from "@/components/system/email/EmailDialog";
import { defaultSettings } from "../../../../convex/system/standardsConfig";
import {
	SelectedItems,
	UebersichtFormState,
} from "./types";

export function UebersichtPage() {
	const [formState, setFormState] = useState<UebersichtFormState>({
		kundeId: "",
		dateRange: "thisMonth",
		startDatum: "",
		endDatum: "",
	});

	useEffect(() => {
		const now = new Date();
		const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
		const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
		setFormState((prev) => ({
			...prev,
			startDatum: startDate.toISOString().split("T")[0],
			endDatum: endDate.toISOString().split("T")[0],
		}));
	}, []);

	const [selectedItems, setSelectedItems] = useState<SelectedItems>({
		leistungen: [],
		kontingente: [],
	});

	// Get settings from the config file
	const uebersichtSettings = defaultSettings.uebersicht;

	// Initialize states with defaults from config
	const [includeSummaryInPDF, setIncludeSummaryInPDF] = useState(
		uebersichtSettings.includeSummary,
	);
	const [includeLeistungsuebersichtInPDF, setIncludeLeistungsuebersichtInPDF] =
		useState(uebersichtSettings.includeLeistungsuebersicht);
	const [
		includeKontingentuebersichtInPDF,
		setIncludeKontingentuebersichtInPDF,
	] = useState(uebersichtSettings.includeKontingentuebersicht);
	const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(
		uebersichtSettings.includeHeader,
	);
	const [includeFooterInPDF, setIncludeFooterInPDF] = useState(
		uebersichtSettings.includeFooter,
	);
	const [showLogoInPDF, setShowLogoInPDF] = useState(
		uebersichtSettings.showLogo,
	);
	const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
		undefined,
	);

	const [hasSearched, setHasSearched] = useState(false);
	const [showPreview, setShowPreview] = useState(false); // State to toggle PDF preview
	const [showEmailDialog, setShowEmailDialog] = useState(false);
	const [pdfBase64, setPdfBase64] = useState<string>("");

	const kunden = useQuery(api.kunden.stammdaten.list) || [];

	// Load logo from the path in config
	useEffect(() => {
		// Get the logo path from the config
		const logoPath = uebersichtSettings.logoPath;
		console.log(`Loading logo from: ${logoPath}`);

		try {
			// In a production app, you would use a more robust way to load images
			// For now, we'll just set the path directly
			// This assumes the logo file exists at the specified path
			setProcessedLogoUrl(logoPath);

			// Create a new Image to check if the URL is valid (optional)
			const img = new Image();
			img.onload = () => {
				console.log("Logo loaded successfully");
			};
			img.onerror = () => {
				console.warn("Failed to load logo image, using fallback");
				// You could set a fallback logo here if needed
			};
			img.src = logoPath;
		} catch (error) {
			console.error("Error loading logo:", error);
			setProcessedLogoUrl(undefined);
		}
	}, []);
	const selectedKunde = useMemo(
		() => kunden.find((kunde) => kunde._id === formState.kundeId),
		[kunden, formState.kundeId],
	);

	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
	const filteredLeistungen = useMemo(() => {
		if (!formState.kundeId || !formState.startDatum || !formState.endDatum)
			return [];
		const startDate = new Date(formState.startDatum);
		startDate.setHours(0, 0, 0, 0);
		const startTimestamp = startDate.getTime();
		const endDate = new Date(formState.endDatum);
		endDate.setHours(23, 59, 59, 999);
		const endTimestamp = endDate.getTime();
		return allLeistungen.filter(
			(l) =>
				l.kundenId === formState.kundeId &&
				l.startZeit >= startTimestamp &&
				l.startZeit <= endTimestamp,
		);
	}, [
		allLeistungen,
		formState.kundeId,
		formState.startDatum,
		formState.endDatum,
	]);

	const allKontingente = useQuery(api.kunden.kontingente.list) || [];
	const filteredKontingente = useMemo(() => {
		if (!formState.kundeId || !formState.startDatum || !formState.endDatum)
			return [];
		const startDate = new Date(formState.startDatum);
		startDate.setHours(0, 0, 0, 0);
		const startTimestamp = startDate.getTime();
		const endDate = new Date(formState.endDatum);
		endDate.setHours(23, 59, 59, 999);
		const endTimestamp = endDate.getTime();
		return allKontingente.filter(
			(k) =>
				k.kundenId === formState.kundeId &&
				((k.startDatum <= endTimestamp && k.endDatum >= startTimestamp) ||
					(k.istAktiv &&
						k.startDatum <= endTimestamp &&
						k.endDatum >= startTimestamp)),
		);
	}, [
		allKontingente,
		formState.kundeId,
		formState.startDatum,
		formState.endDatum,
	]);

	const handleFormChange = (key: keyof UebersichtFormState, value: any) => {
		setFormState((prev) => ({ ...prev, [key]: value }));
		setHasSearched(false); // Reset search state if form changes
		setShowPreview(false); // Hide preview if form changes
	};

	const handleFormSubmit = () => {
		if (!formState.kundeId) {
			toast.error("Bitte wählen Sie einen Kunden aus.");
			return;
		}
		const leistungenIds = filteredLeistungen.map((l) => l._id);
		const kontingenteIds = filteredKontingente.map((k) => k._id);
		setSelectedItems({
			leistungen: leistungenIds,
			kontingente: kontingenteIds,
		});
		setHasSearched(true);
		setShowPreview(false); // Initially, don't show preview, only after "Daten laden" and then "Vorschau anzeigen"
	};

	const handleLeistungSelectionChange = (
		id: Id<"kunden_leistungen">,
		selected: boolean,
	) => {
		setSelectedItems((prev) => ({
			...prev,
			leistungen: selected
				? [...prev.leistungen, id]
				: prev.leistungen.filter((lId) => lId !== id),
		}));
	};

	const handleKontingentSelectionChange = (
		id: Id<"kunden_kontingente">,
		selected: boolean,
	) => {
		setSelectedItems((prev) => ({
			...prev,
			kontingente: selected
				? [...prev.kontingente, id]
				: prev.kontingente.filter((kId) => kId !== id),
		}));
	};

	const handleSelectAllLeistungen = (selected: boolean) => {
		setSelectedItems((prev) => ({
			...prev,
			leistungen: selected ? filteredLeistungen.map((l) => l._id) : [],
		}));
	};

	const handleSelectAllKontingente = (selected: boolean) => {
		setSelectedItems((prev) => ({
			...prev,
			kontingente: selected ? filteredKontingente.map((k) => k._id) : [],
		}));
	};

	const selectedLeistungenData = useMemo(
		() =>
			filteredLeistungen.filter((l) =>
				selectedItems.leistungen.includes(l._id),
			),
		[filteredLeistungen, selectedItems.leistungen],
	);

	const selectedKontingenteData = useMemo(
		() =>
			filteredKontingente.filter((k) =>
				selectedItems.kontingente.includes(k._id),
			),
		[filteredKontingente, selectedItems.kontingente],
	);

	const formatDateRange = () =>
		formatDateRangeDE(formState.startDatum, formState.endDatum);

	// Include a timestamp in the key to ensure re-rendering when checkbox changes
	const pdfKey = `pdf-${formState.kundeId}-${formState.startDatum}-${formState.endDatum}-${selectedItems.leistungen.length}-${selectedItems.kontingente.length}-${includeSummaryInPDF}-${includeLeistungsuebersichtInPDF}-${includeKontingentuebersichtInPDF}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${Date.now()}`;

	const documentInstance = selectedKunde ? (
		<PDFDocumentComponent
			key={pdfKey} // Ensure re-creation on data change for PDFDownloadLink
			kundeName={selectedKunde.name}
			firmenName="innov8-IT" // Use company name directly
			logoUrl={processedLogoUrl} // Pass processed logo URL
			showLogo={showLogoInPDF} // Pass showLogo state directly
			zeitraum={formatDateRange()}
			leistungen={selectedLeistungenData}
			kontingente={selectedKontingenteData}
			includeSummary={includeSummaryInPDF}
			includeLeistungsuebersicht={includeLeistungsuebersichtInPDF}
			includeKontingentuebersicht={includeKontingentuebersichtInPDF}
			includeHeader={includeHeaderInPDF}
			includeFooter={includeFooterInPDF}
			formatDate={formatDate}
			formatTime={formatTime}
			formatHours={formatHours}
			formatCurrency={formatCurrency}
			// Pass settings from config
			firmenFusszeileText={uebersichtSettings.fusszeileText}
		/>
	) : null;

	const pdfFileName = selectedKunde
		? `Uebersicht_${selectedKunde.name.replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}.pdf`
		: "Uebersicht.pdf";

	// Function to generate PDF as Base64 for email
	const generatePdfBase64 = async () => {
		if (!documentInstance) return "";
		try {
			const blob = await pdf(documentInstance).toBlob();
			const buffer = await blob.arrayBuffer();
			const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
			return base64;
		} catch (error) {
			console.error("Error generating PDF Base64:", error);
			toast.error("Fehler beim Generieren der PDF");
			return "";
		}
	};

	// Function to handle email button click
	const handleEmailClick = async () => {
		const base64 = await generatePdfBase64();
		if (base64) {
			setPdfBase64(base64);
			setShowEmailDialog(true);
		}
	};

	return (
		<PageLayout
			title="Übersicht Erstellung"
			subtitle="Interaktive Erstellung von Leistungs- und Kontingentübersichten"
		>
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Left Column: Controls and Options */}
				<div className="lg:col-span-1 space-y-6">
					<Card className="shadow-lg border-0">
						<CardHeader className="p-4">
							<CardTitle className="text-base font-medium">
								1. Daten auswählen
							</CardTitle>
						</CardHeader>
						<CardContent className="p-4">
							<UebersichtForm
								formState={formState}
								onFormChange={handleFormChange}
								onSubmit={handleFormSubmit}
								buttonLabel="Daten für Übersicht laden"
							/>
						</CardContent>
					</Card>

					{hasSearched && selectedKunde && (
						<Card className="shadow-lg border-0">
							<CardHeader className="p-4">
								<CardTitle className="text-base font-medium">
									2. PDF-Inhalt konfigurieren
								</CardTitle>
							</CardHeader>
							<CardContent className="p-4 space-y-3">
								<div className="flex items-center space-x-2">
									<Checkbox
										id="includeHeader"
										checked={includeHeaderInPDF}
										onCheckedChange={(c) => setIncludeHeaderInPDF(!!c)}
									/>
									<Label htmlFor="includeHeader" className="text-sm">
										Header einblenden
									</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="showLogoInPDF"
										checked={showLogoInPDF}
										onCheckedChange={(c) => {
											setShowLogoInPDF(!!c);
											// Force PDF preview to update by toggling and restoring showPreview
											if (showPreview) {
												setShowPreview(false);
												setTimeout(() => setShowPreview(true), 50);
											}
										}}
										disabled={!processedLogoUrl}
									/>
									<Label
										htmlFor="showLogoInPDF"
										className={`text-sm ${!processedLogoUrl ? "opacity-50" : ""}`}
									>
										Firmenlogo im Header anzeigen
										{!processedLogoUrl &&
											" (Logo wird aus der Konfiguration geladen)"}
									</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="includeLeistungsuebersicht"
										checked={includeLeistungsuebersichtInPDF}
										onCheckedChange={(c) =>
											setIncludeLeistungsuebersichtInPDF(!!c)
										}
										disabled={selectedLeistungenData.length === 0}
									/>
									<Label
										htmlFor="includeLeistungsuebersicht"
										className={`text-sm ${selectedLeistungenData.length === 0 ? "opacity-50" : ""}`}
									>
										Leistungsübersicht
									</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="includeKontingentuebersicht"
										checked={includeKontingentuebersichtInPDF}
										onCheckedChange={(c) =>
											setIncludeKontingentuebersichtInPDF(!!c)
										}
										disabled={selectedKontingenteData.length === 0}
									/>
									<Label
										htmlFor="includeKontingentuebersicht"
										className={`text-sm ${selectedKontingenteData.length === 0 ? "opacity-50" : ""}`}
									>
										Kontingentübersicht
									</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="includeSummary"
										checked={includeSummaryInPDF}
										onCheckedChange={(c) => setIncludeSummaryInPDF(!!c)}
									/>
									<Label htmlFor="includeSummary" className="text-sm">
										Zusammenfassung
									</Label>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="includeFooter"
										checked={includeFooterInPDF}
										onCheckedChange={(c) => setIncludeFooterInPDF(!!c)}
									/>
									<Label htmlFor="includeFooter" className="text-sm">
										Fußzeile einblenden
									</Label>
								</div>
								<div className="flex space-x-2 pt-3 border-t border-gray-700/50">
									<Button
										onClick={() => setShowPreview(!showPreview)}
										variant="outline"
										className="h-10 w-10 p-0"
										title={
											showPreview ? "Vorschau ausblenden" : "Vorschau anzeigen"
										}
									>
										<Eye className="h-5 w-5" />
									</Button>
									{documentInstance && (
										<PDFDownloadLink
											key={`download-${pdfKey}`}
											document={documentInstance}
											fileName={pdfFileName}
										>
											{({ loading }) => (
												<Button
													disabled={loading}
													className="h-10 w-10 p-0"
													title="PDF herunterladen"
												>
													<Download className="h-5 w-5" />
												</Button>
											)}
										</PDFDownloadLink>
									)}
									{documentInstance && (
										<Button
											onClick={handleEmailClick}
											variant="outline"
											className="h-10 w-10 p-0"
											title="Per E-Mail versenden"
										>
											<Mail className="h-5 w-5" />
										</Button>
									)}
								</div>
							</CardContent>
						</Card>
					)}
				</div>

				{/* Right Column: PDF Preview */}
				<div className="lg:col-span-2">
					{hasSearched && showPreview && selectedKunde && documentInstance ? (
						<Card className="shadow-lg border-0 h-[calc(100vh-12rem)]">
							{/* Ensure PDFViewer is only mounted on client */}
							{typeof window !== "undefined" && (
								<PDFViewer
									key={pdfKey} // Force re-render on data change
									width="100%"
									height="100%"
									className="rounded-md"
									showToolbar={true}
								>
									{documentInstance}
								</PDFViewer>
							)}
						</Card>
					) : hasSearched ? (
						<Card className="shadow-lg border-0 flex items-center justify-center h-[calc(100vh-12rem)] bg-gray-800/30">
							<div className="text-center text-gray-400">
								<Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
								<p className="font-medium">PDF-Vorschau</p>
								<p className="text-sm">
									Klicken Sie auf "Vorschau", um das Dokument zu sehen.
								</p>
							</div>
						</Card>
					) : (
						<Card className="shadow-lg border-0 flex items-center justify-center h-[calc(100vh-12rem)] bg-gray-800/30">
							<div className="text-center text-gray-400">
								<p>
									Bitte laden Sie zuerst Daten, um eine Übersicht zu erstellen.
								</p>
							</div>
						</Card>
					)}
				</div>
			</div>

			{/* Data Selection Lists (Leistungen, Kontingente, Summary) - below the main layout */}
			{hasSearched && (
				<div className="mt-6 space-y-6">
					{filteredLeistungen.length === 0 &&
					filteredKontingente.length === 0 ? (
						<Card className="shadow-lg border-0">
							<CardContent className="p-8 text-center">
								<p className="text-gray-400">
									Keine Daten für den ausgewählten Zeitraum und Kunden gefunden.
								</p>
							</CardContent>
						</Card>
					) : (
						<>
							{filteredLeistungen.length > 0 && (
								<LeistungList
									leistungen={filteredLeistungen}
									selectedLeistungen={selectedItems.leistungen}
									onSelectionChange={handleLeistungSelectionChange}
									onSelectAll={handleSelectAllLeistungen}
									formatDate={formatDate}
									formatTime={formatTime}
									formatHours={formatHours}
									formatCurrency={formatCurrency}
								/>
							)}
							{filteredKontingente.length > 0 && (
								<KontingentList
									kontingente={filteredKontingente}
									selectedKontingente={selectedItems.kontingente}
									onSelectionChange={handleKontingentSelectionChange}
									onSelectAll={handleSelectAllKontingente}
									formatDate={formatDate}
								/>
							)}
							{(selectedLeistungenData.length > 0 ||
								selectedKontingenteData.length > 0) && (
								<SummarySection
									leistungen={selectedLeistungenData}
									kontingente={selectedKontingenteData}
									includeSummaryInPDF={includeSummaryInPDF} // This prop might be less relevant here now
									onIncludeSummaryChange={setIncludeSummaryInPDF} // This prop might be less relevant here now
									formatHours={formatHours}
								/>
							)}
						</>
					)}
				</div>
			)}

		{/* Email Dialog */}
		{showEmailDialog && selectedKunde && (
			<EmailDialog
				isOpen={showEmailDialog}
				onClose={() => setShowEmailDialog(false)}
				type="uebersicht"
				kundeId={selectedKunde._id}
				zeitraum={formatDateRange()}
				pdfBase64={pdfBase64}
			/>
		)}
	</PageLayout>
	);
}

export default UebersichtPage;
