import { api } from "@/../convex/_generated/api";
import type { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Card, CardHeader, CardTitle } from "@/components/_shared/Card";
import { KontingentDataTable } from "@/components/kunden/kontingente/KontingentDataTable";
import { KontingentFilterControls } from "@/components/kunden/kontingente/KontingentFilterControls";
import { KontingentForm } from "@/components/kunden/kontingente/KontingentForm";
import { EmptyState } from "@/components/layout/EmptyState"; // Import new EmptyState
import { PageLayout } from "@/components/layout/PageLayout";
import { formatDate, toInputDate } from "@/lib/utils/formatUtils";
import { useMutation, useQuery } from "convex/react";
import {
	CalendarClock,
	Clock,
	Hourglass, // Added Hourglass
	PlusCircle,
	TimerIcon,
	X,
	Zap,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

// Typ angepasst, um mit der Convex-Funktion `list` übereinzustimmen
interface KontingentMitKunde {
	_id: Id<"kunden_kontingente">;
	_creationTime: number;
	kundenId: Id<"kunden">;
	name: string;
	stunden: number;
	verbrauchteStunden: number;
	startDatum: number; // Timestamp
	endDatum: number; // Timestamp
	istAktiv: boolean;
	kundeName: string;
	restStunden: number;
}

interface KontingentFilter {
	kundeId: string;
	zeitraum: "all" | "active" | "inactive" | "upcoming" | "expired" | "custom";
	startDatum: string;
	endDatum: string;
}

export function KontingentePage() {
	const kontingente = useQuery(api.kunden.kontingente.list) || [];
	const kunden = useQuery(api.kunden.stammdaten.list) || [];
	const removeKontingent = useMutation(api.kunden.kontingente.remove);

	const [editingId, setEditingId] = useState<Id<"kunden_kontingente"> | null>(
		null,
	);

	// Separate state for non-form UI elements
	const [showForm, setShowForm] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [filter, setFilter] = useState<KontingentFilter>({
		kundeId: "",
		zeitraum: "active", // Default to active
		startDatum: "",
		endDatum: "",
	});

	// Find the kontingent object to pass to the form when editing
	const editingKontingent = useMemo(() => {
		return editingId
			? (kontingente.find((k) => k._id === editingId) ?? null)
			: null;
	}, [editingId, kontingente]);

	// Handlers for form component callbacks
	const handleFormSubmitSuccess = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleFormCancel = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleEdit = (kontingent: KontingentMitKunde) => {
		setEditingId(kontingent._id);
		setShowForm(true);
	};

	const handleDelete = async (id: Id<"kunden_kontingente">) => {
		if (window.confirm("Möchten Sie dieses Kontingent wirklich löschen?")) {
			try {
				await removeKontingent({ id });
				toast.success("Kontingent erfolgreich gelöscht");
				if (id === editingId) {
					setShowForm(false);
					setEditingId(null);
				}
			} catch (error: any) {
				toast.error(
					`Fehler beim Löschen: ${error.message || "Unbekannter Fehler"}`,
				);
			}
		}
	};

	const getStatusInfo = (
		kontingent: KontingentMitKunde,
	): { text: string; color: string; icon: React.ReactNode } => {
		const now = Date.now();
		if (!kontingent.istAktiv)
			return {
				text: "Inaktiv",
				color: "bg-gray-500/20 text-gray-400",
				icon: <X className="h-3 w-3" />,
			};
		if (kontingent.startDatum > now)
			return {
				text: "Zukünftig",
				color: "bg-blue-500/20 text-blue-300",
				icon: <CalendarClock className="h-3 w-3" />,
			};
		if (kontingent.endDatum < now)
			return {
				text: "Abgelaufen",
				color: "bg-red-500/20 text-red-400",
				icon: <Clock className="h-3 w-3" />,
			};
		return {
			text: "Aktiv",
			color: "bg-green-500/20 text-green-300",
			icon: <Zap className="h-3 w-3" />,
		};
	};

	// --- Prepare props for KontingentForm --- START
	// Define the shape KontingentForm expects (adjust if needed)
	type KontingentFormData = {
		kundeId: string;
		kontingentName: string;
		stunden: string;
		verbrauchteStunden: string;
		startDatumInput: string;
		endDatumInput: string; // New field
		istAktiv: boolean;
	};

	// Default empty/initial state for a new Kontingent
	const defaultKontingentFormData: KontingentFormData = useMemo(() => {
		const heute = new Date();
		const endDatumDefault = new Date(heute);
		endDatumDefault.setDate(heute.getDate() + 90); // Default 90 days

		return {
			kundeId: "",
			kontingentName: "",
			stunden: "",
			verbrauchteStunden: "0", // Default to 0
			startDatumInput: toInputDate(heute.getTime()),
			endDatumInput: toInputDate(endDatumDefault.getTime()),
			istAktiv: true,
		};
	}, []);

	// Calculate the initialData prop based on editing state
	const formDataForForm = useMemo(() => {
		if (editingKontingent) {
			// Map editingKontingent to KontingentFormData
			return {
				kundeId: editingKontingent.kundenId,
				kontingentName: editingKontingent.name,
				stunden: editingKontingent.stunden.toString(),
				verbrauchteStunden: editingKontingent.verbrauchteStunden.toString(),
				startDatumInput: toInputDate(editingKontingent.startDatum),
				endDatumInput: toInputDate(editingKontingent.endDatum),
				istAktiv: editingKontingent.istAktiv,
			};
		} else {
			// Return default for new form (includes calculated end date)
			return defaultKontingentFormData;
		}
	}, [editingKontingent, defaultKontingentFormData]);
	// --- Prepare props for KontingentForm --- END

	// Callback to reset filters, passed to EmptyState
	const resetFilters = () => {
		setSearchTerm("");
		setFilter({
			kundeId: "",
			zeitraum: "active",
			startDatum: "",
			endDatum: "",
		});
	};

	const filteredKontingente = useMemo(() => {
		const now = Date.now();
		return kontingente.filter((k) => {
			// Search Term Filter (Kunde, Kontingent Name)
			const searchMatch =
				k.kundeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				k.name.toLowerCase().includes(searchTerm.toLowerCase());

			// Customer Filter
			const kundeMatch = !filter.kundeId || k.kundenId === filter.kundeId;

			// Date/Status Filter
			let dateMatch = true;
			switch (filter.zeitraum) {
				case "active":
					dateMatch = k.istAktiv && k.startDatum <= now && k.endDatum >= now;
					break;
				case "inactive":
					dateMatch = !k.istAktiv;
					break;
				case "upcoming":
					dateMatch = k.istAktiv && k.startDatum > now;
					break;
				case "expired":
					dateMatch = k.endDatum < now; // Kann auch inaktiv sein
					break;
				case "custom": {
					const startTime = filter.startDatum
						? new Date(filter.startDatum).getTime()
						: 0;
					// Add 1 day (in milliseconds) to end date to include the whole day
					const endTime = filter.endDatum
						? new Date(filter.endDatum).getTime() + 86400000 - 1
						: new Date(8640000000000000).getTime();
					// Check if kontingent *overlaps* with the custom range (start or end date within range)
					dateMatch =
						(k.startDatum >= startTime && k.startDatum <= endTime) ||
						(k.endDatum >= startTime && k.endDatum <= endTime) ||
						(k.startDatum < startTime && k.endDatum > endTime);
					break;
				}
				case "all": // Default
				default:
					dateMatch = true;
					break;
			}

			return searchMatch && kundeMatch && dateMatch;
		});
	}, [kontingente, searchTerm, filter]);

	// Handler for filter changes (passed to KontingentFilterControls)
	const handleFilterChange = (field: keyof KontingentFilter, value: string) => {
		setFilter((prev) => ({ ...prev, [field]: value }));
	};

	// Handler for search term changes (passed to KontingentFilterControls)
	const handleSearchTermChange = (value: string) => {
		setSearchTerm(value);
	};

	const totalStunden = filteredKontingente.reduce(
		(acc, k) => acc + k.stunden,
		0,
	);
	const totalVerbraucht = filteredKontingente.reduce(
		(acc, k) => acc + k.verbrauchteStunden,
		0,
	);
	const totalRest = totalStunden - totalVerbraucht;

	const actionButton = (
		<Button
			onClick={() => {
				setShowForm(!showForm);
				if (showForm) handleFormCancel(); // Reset form on close
			}}
			size="sm"
			className="gap-1"
		>
			{showForm ? (
				<>
					<X className="h-4 w-4" /> Abbrechen
				</>
			) : (
				<>
					<PlusCircle className="h-4 w-4" /> Neues Kontingent
				</>
			)}
		</Button>
	);

	return (
		<PageLayout
			title="Kontingente"
			subtitle="Verwalten Sie hier Ihre Kundenkontingente"
			action={actionButton}
		>
			{showForm && (
				<KontingentForm
					editingKontingent={editingKontingent}
					isEditing={!!editingId}
					initialData={formDataForForm}
					kunden={kunden}
					onSubmitSuccess={handleFormSubmitSuccess}
					onCancel={handleFormCancel}
				/>
			)}

			<Card className="shadow-lg border-0 overflow-hidden">
				<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
					<div className="flex flex-col gap-4">
						<div className="flex flex-wrap items-center justify-between">
							<CardTitle className="text-lg font-medium">
								Kontingentübersicht
							</CardTitle>
							<div className="flex items-center gap-2 text-xs text-gray-400 bg-gray-800/70 px-3 py-1 rounded-full">
								<TimerIcon className="h-3.5 w-3.5 opacity-70" />
								<span>
									{filteredKontingente.length} Einträge | {totalRest.toFixed(2)}{" "}
									h Rest
								</span>
							</div>
						</div>
						{/* Render Filter Controls Component */}
						<KontingentFilterControls
							filter={filter}
							searchTerm={searchTerm}
							kunden={kunden}
							onFilterChange={handleFilterChange}
							onSearchTermChange={handleSearchTermChange}
						/>
					</div>
				</CardHeader>

				{filteredKontingente.length === 0 ? (
					<EmptyState
						icon={<Hourglass className="w-12 h-12" />} // Adjusted icon size
						title="Keine Kontingente gefunden"
						message={
							searchTerm || filter.kundeId || filter.zeitraum !== "active" // Original logic for 'active' default
								? "Versuchen Sie, Ihre Filterkriterien anzupassen"
								: "Erstellen Sie ein neues Kontingent mit dem Button oben rechts"
						}
						actions={
							(searchTerm ||
								filter.kundeId ||
								filter.zeitraum !== "active") && ( // Original logic for 'active' default
								<Button variant="outline" size="sm" onClick={resetFilters}>
									Filter zurücksetzen
								</Button>
							)
						}
					/>
				) : (
					<KontingentDataTable
						kontingente={filteredKontingente}
						onEdit={handleEdit}
						onDelete={handleDelete}
						formatDate={formatDate}
						getStatusInfo={getStatusInfo}
					/>
				)}
			</Card>
		</PageLayout>
	);
}

export default KontingentePage;
