import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { PageLayout } from "@/components/layout/PageLayout";
import { KundenPageForm } from "@/components/kunden/stammdaten/KundenPageForm";
import { useMutation, useQuery } from "convex/react";
import { ArrowLeft, Save, Trash2 } from "lucide-react";
import { useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

export function KundenStammdatenDetailPage() {
	const { id } = useParams<{ id: string }>();
	const navigate = useNavigate();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Convert id to proper type
	const kundeId = id as Id<"kunden">;

	// Get customer data
	const kunde = useQuery(api.kunden.stammdaten.get, { id: kundeId });

	// Mutations
	const updateKunde = useMutation(api.kunden.stammdaten.update);
	const deleteKunde = useMutation(api.kunden.stammdaten.remove);

	const handleSave = async (data: any) => {
		if (!kunde) return;

		setIsSubmitting(true);
		try {
			await updateKunde({
				id: kunde._id,
				...data,
			});
			toast.success("Kunde erfolgreich aktualisiert");
			navigate("/kunden/stammdaten");
		} catch (error: any) {
			toast.error(`Fehler beim Speichern: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleDelete = async () => {
		if (!kunde) return;

		if (!confirm("Sind Sie sicher, dass Sie diesen Kunden löschen möchten?")) {
			return;
		}

		setIsSubmitting(true);
		try {
			await deleteKunde({ id: kunde._id });
			toast.success("Kunde erfolgreich gelöscht");
			navigate("/kunden/stammdaten");
		} catch (error: any) {
			toast.error(`Fehler beim Löschen: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	if (!kunde) {
		return (
			<PageLayout title="Kunde wird geladen..." subtitle="Bitte warten">
				<div className="flex items-center justify-center h-64">
					<div className="text-gray-400">Lade Kundendaten...</div>
				</div>
			</PageLayout>
		);
	}

	const headerActions = (
		<div className="flex items-center gap-2">
			<Link to="/kunden/stammdaten">
				<Button variant="outline" className="gap-2">
					<ArrowLeft className="h-4 w-4" />
					Zurück
				</Button>
			</Link>

			<Button
				variant="destructive"
				onClick={handleDelete}
				disabled={isSubmitting}
				className="gap-2"
			>
				<Trash2 className="h-4 w-4" />
				Löschen
			</Button>

			<Button
				form="kunden-form"
				type="submit"
				disabled={isSubmitting}
				className="gap-2"
			>
				<Save className="h-4 w-4" />
				{isSubmitting ? "Speichert..." : "Speichern"}
			</Button>
		</div>
	);

	return (
		<PageLayout
			title={`Kunde bearbeiten: ${kunde.name}`}
			subtitle="Stammdaten bearbeiten"
			action={headerActions}
		>
			<div className="max-w-6xl mx-auto">
				{/* Form */}
				<KundenPageForm
					initialData={kunde}
					onSubmit={handleSave}
					isSubmitting={isSubmitting}
					formId="kunden-form"
				/>
			</div>
		</PageLayout>
	);
}

export default KundenStammdatenDetailPage;
