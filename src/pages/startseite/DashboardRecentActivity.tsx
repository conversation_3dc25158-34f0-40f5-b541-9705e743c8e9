import { api } from "@/../convex/_generated/api";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Skeleton } from "@/components/_shared/Skeleton";
import { formatDate, formatHours, formatTime } from "@/lib/utils/formatUtils";
import { useQuery } from "convex/react";
import { Briefcase, Calendar, Clock, Info, User, Users } from "lucide-react";
import { useMemo } from "react";

interface DashboardRecentActivityProps {
	filter: {
		zeitraum: string;
		startDatum: string;
		endDatum: string;
		kundeId: string;
		mitarbeiterId: string;
	};
	isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-4 border-b border-gray-700/50">
			<Skeleton className="h-6 w-48" />
		</CardHeader>
		<CardContent className="p-0">
			<div className="p-4 space-y-4">
				{[...Array(5)].map((_, i) => (
					<div key={i} className="space-y-2">
						<Skeleton className="h-5 w-full" />
						<Skeleton className="h-4 w-3/4" />
						<Skeleton className="h-4 w-1/2" />
					</div>
				))}
			</div>
		</CardContent>
	</Card>
);

// Empty state component
const EmptyState = () => (
	<Card className="border-dashed border-gray-700 bg-gray-800/30 h-full">
		<CardContent className="py-12 flex flex-col items-center justify-center text-center">
			<Info className="w-10 h-10 text-gray-500 mb-3" />
			<p className="text-base font-medium text-gray-300">Keine Aktivitäten</p>
			<p className="text-sm text-gray-400 mt-1">
				Für den ausgewählten Zeitraum wurden keine Aktivitäten gefunden.
			</p>
		</CardContent>
	</Card>
);

export function DashboardRecentActivity({
	filter,
	isLoading,
}: DashboardRecentActivityProps) {
	// Fetch all leistungen
	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];

	// Filter and sort leistungen based on the filter criteria
	const recentLeistungen = useMemo(() => {
		if (!allLeistungen) return [];

		// Filter leistungen based on date range and selected filters
		const filteredLeistungen = allLeistungen.filter((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			const startDate = new Date(filter.startDatum);
			const endDate = new Date(filter.endDatum);

			// Check if leistung is within date range
			const isInDateRange =
				leistungDatum >= startDate && leistungDatum <= endDate;

			// Check if leistung matches selected kunde
			const matchesKunde =
				filter.kundeId === "all" || leistung.kundenId === filter.kundeId;

			// Check if leistung matches selected mitarbeiter
			const matchesMitarbeiter =
				filter.mitarbeiterId === "all" ||
				leistung.mitarbeiterId === filter.mitarbeiterId;

			return isInDateRange && matchesKunde && matchesMitarbeiter;
		});

		// Sort by date (newest first)
		return filteredLeistungen
			.sort((a, b) => b.startZeit - a.startZeit)
			.slice(0, 10); // Get only the 10 most recent
	}, [allLeistungen, filter]);

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (!recentLeistungen || recentLeistungen.length === 0) {
		return <EmptyState />;
	}

	return (
		<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden h-full">
			<CardHeader className="p-3 border-b border-gray-700/50">
				<CardTitle className="text-sm font-medium flex items-center">
					<Calendar className="w-4 h-4 mr-1.5" />
					Letzte Aktivitäten
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				<div className="divide-y divide-gray-700/50">
					{recentLeistungen.map((leistung) => {
						const date = new Date(leistung.startZeit);
						// Bestimme den Leistungstyp (Remote/Vor-Ort/Vor-Ort (free))
						let leistungsTyp = "Remote";
						if (leistung.mitAnfahrt) {
							leistungsTyp =
								leistung.anfahrtskosten > 0 ? "Vor-Ort" : "Vor-Ort (free)";
						}

						return (
							<div key={leistung._id} className="p-2 hover:bg-gray-700/20">
								<div className="flex justify-between items-center">
									<div className="flex items-center gap-1.5">
										<User className="w-3 h-3 text-gray-400" />
										<span className="text-xs">{leistung.mitarbeiterName}</span>
									</div>
									<div className="text-xs text-gray-400">
										{formatDate(date)}
									</div>
								</div>
								<div className="flex justify-between items-center mt-1">
									<div className="flex items-center gap-1.5">
										<Clock className="w-3 h-3 text-gray-400" />
										<span className="text-xs">
											{formatHours(leistung.stunden)}
										</span>
									</div>
									<div className="text-xs text-gray-400">{leistungsTyp}</div>
								</div>
							</div>
						);
					})}
				</div>
			</CardContent>
		</Card>
	);
}
