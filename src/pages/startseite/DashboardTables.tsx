import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Progress } from "@/components/_shared/Progress";
import { Skeleton } from "@/components/_shared/Skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { Hourglass, Info, Users } from "lucide-react";

interface KundenStatistik {
	kundeId: string;
	kundeName: string;
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
}

interface MitarbeiterStatistik {
	mitarbeiterId: string;
	mitarbeiterName: string;
	stunden: number;
	anzahlLeistungen: number;
}

interface KontingentProKundeStatistik {
	kundeId: string;
	kundeName: string;
	gesamtStunden: number;
	verbrauchtStunden: number;
	restStunden: number;
	anzahlKontingente: number;
}

interface DashboardDaten {
	gesamtStundenLeistung: number;
	gesamtUmsatz: number;
	anzahlLeistungenGesamt: number;
	aktiveKundenCount: number;
	aktiveMitarbeiterCount: number;
	anzahlAnfahrten: number;
	kundenStatistiken: KundenStatistik[];
	mitarbeiterStatistiken: MitarbeiterStatistik[];
	kontingentStatistiken: KontingentProKundeStatistik[];
	topLeistungenKunden: KundenStatistik[];
	topLeistungenMitarbeiter: MitarbeiterStatistik[];
}

interface DashboardTablesProps {
	dashboardDaten: DashboardDaten | null;
	isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-4 border-b border-gray-700/50">
			<Skeleton className="h-6 w-48" />
		</CardHeader>
		<CardContent className="p-0">
			<div className="p-4">
				{[...Array(5)].map((_, i) => (
					<div key={i} className="flex justify-between py-2">
						<Skeleton className="h-5 w-32" />
						<Skeleton className="h-5 w-16" />
					</div>
				))}
			</div>
		</CardContent>
	</Card>
);

// Empty state component
const EmptyState = () => (
	<Card className="border-dashed border-gray-700 bg-gray-800/30">
		<CardContent className="py-12 flex flex-col items-center justify-center text-center">
			<Info className="w-10 h-10 text-gray-500 mb-3" />
			<p className="text-base font-medium text-gray-300">
				Keine Daten verfügbar
			</p>
			<p className="text-sm text-gray-400 mt-1">
				Für den ausgewählten Zeitraum und Filter wurden keine Daten gefunden.
			</p>
		</CardContent>
	</Card>
);

export function DashboardTables({
	dashboardDaten,
	isLoading,
}: DashboardTablesProps) {
	if (isLoading) {
		return <LoadingSkeleton />;
	}

	if (!dashboardDaten || dashboardDaten.anzahlLeistungenGesamt === 0) {
		return <EmptyState />;
	}

	const { topLeistungenKunden, kontingentStatistiken } = dashboardDaten;

	// Sort kontingente by usage percentage (descending)
	const sortedKontingente = [...kontingentStatistiken]
		.filter((k) => k.gesamtStunden > 0) // Only include kontingente with hours
		.sort((a, b) => {
			const usageA = a.verbrauchtStunden / a.gesamtStunden;
			const usageB = b.verbrauchtStunden / b.gesamtStunden;
			return usageB - usageA;
		})
		.slice(0, 5); // Top 5

	return (
		<div className="space-y-4">
			{/* Kunden Tabelle */}
			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<CardTitle className="text-sm font-medium flex items-center">
						<Users className="w-4 h-4 mr-1.5" /> Kunden
					</CardTitle>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-gray-700/50">
								<TableHead className="w-[50%] px-3 py-2 text-xs font-medium text-gray-400">
									Kunde
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400 text-right">
									Stunden
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400 text-right">
									Umsatz
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400 text-right">
									Leistungen
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{topLeistungenKunden.map((k) => (
								<TableRow
									key={k.kundeId}
									className="border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30"
								>
									<TableCell className="px-3 py-2 text-xs font-medium">
										{k.kundeName}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatHours(k.stunden)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatCurrency(k.umsatz)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{k.anzahlLeistungen}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</CardContent>
			</Card>

			{/* Kontingente Tabelle */}
			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<CardTitle className="text-sm font-medium flex items-center">
						<Hourglass className="w-4 h-4 mr-1.5" /> Kontingente
					</CardTitle>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-gray-700/50">
								<TableHead className="w-[40%] px-3 py-2 text-xs font-medium text-gray-400">
									Kunde
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400 text-right">
									Verbraucht
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400 text-right">
									Rest
								</TableHead>
								<TableHead className="px-3 py-2 text-xs font-medium text-gray-400">
									Auslastung
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{sortedKontingente.map((k) => {
								const usagePercentage = Math.min(
									100,
									Math.round((k.verbrauchtStunden / k.gesamtStunden) * 100),
								);
								return (
									<TableRow
										key={k.kundeId}
										className="border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30"
									>
										<TableCell className="px-3 py-2 text-xs font-medium">
											{k.kundeName} ({k.anzahlKontingente})
										</TableCell>
										<TableCell className="px-3 py-2 text-xs text-right">
											{formatHours(k.verbrauchtStunden)}
										</TableCell>
										<TableCell className="px-3 py-2 text-xs text-right">
											{formatHours(k.restStunden)}
										</TableCell>
										<TableCell className="px-3 py-2 text-xs">
											<div className="flex items-center gap-2">
												<Progress
													value={usagePercentage}
													className="h-1.5 flex-1"
													indicatorClassName={
														usagePercentage > 90
															? "bg-red-500"
															: usagePercentage > 75
																? "bg-orange-500"
																: "bg-blue-500"
													}
												/>
												<span className="text-xs w-9 text-right">
													{usagePercentage}%
												</span>
											</div>
										</TableCell>
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		</div>
	);
}
