import { api } from "@/../convex/_generated/api";
import { FIELD_TYPES } from "@/../convex/schema";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { PageLayout } from "@/components/layout/PageLayout";
import { DokuKategorienTable } from "@/components/system/doku-kategorien/DokuKategorienTable";
import { cn } from "@/lib/utils/cn";
import { useMutation, useQuery } from "convex/react";
import { RefreshCw, Database, Info } from "lucide-react";
import {
	AlertTriangle,
	Copy,
	EyeOff,
	ListChecks,
	MessageSquareText,
	Sparkles,
} from "lucide-react";
import { useMemo, useRef, useState } from "react";
import { toast } from "sonner";

// Define fieldTypeColors
const fieldTypeColors: Record<string, string> = {
	[FIELD_TYPES.TEXT]: "bg-sky-500/20 text-sky-300 border-sky-500/30",
	[FIELD_TYPES.TEXTAREA]: "bg-teal-500/20 text-teal-300 border-teal-500/30",
	[FIELD_TYPES.NUMBER]: "bg-amber-500/20 text-amber-300 border-amber-500/30",
	[FIELD_TYPES.PASSWORD]: "bg-rose-500/20 text-rose-300 border-rose-500/30",
	[FIELD_TYPES.URL]: "bg-indigo-500/20 text-indigo-300 border-indigo-500/30",
	[FIELD_TYPES.EMAIL]: "bg-purple-500/20 text-purple-300 border-purple-500/30",
	[FIELD_TYPES.DATE]: "bg-lime-500/20 text-lime-300 border-lime-500/30",
	[FIELD_TYPES.CHECKBOX]: "bg-pink-500/20 text-pink-300 border-pink-500/30",
	[FIELD_TYPES.SELECT]: "bg-cyan-500/20 text-cyan-300 border-cyan-500/30",
	[FIELD_TYPES.PHONE]: "bg-orange-500/20 text-orange-300 border-orange-500/30",
	default: "bg-gray-700 text-gray-200 border-gray-600",
};

export function DokuKategorienPage() {
	const kategorien = useQuery(api.system.dokuKategorien.list) || [];
	const initializeDefaults = useMutation(
		api.system.dokuKategorien.initializeDefaults,
	);
	const pageTopRef = useRef<HTMLDivElement>(null);
	const [isSynchronizing, setIsSynchronizing] = useState(false);
	const [showLegend, setShowLegend] = useState(false);

	// Just show all categories sorted by reihenfolge
	const filteredKategorien = useMemo(() => {
		return kategorien;
	}, [kategorien]);

	const handleSynchronize = async () => {
		if (
			window.confirm(
				"Möchten Sie die Kategorien wirklich mit der Konfigurationsdatei synchronisieren? " +
					"Neue werden hinzugefügt, bestehende Kategorien ggf. aktualisiert. " +
					"Nicht mehr in der Konfigurationsdatei enthaltene Kategorien werden entfernt, falls sie nicht verwendet werden.",
			)
		) {
			setIsSynchronizing(true);
			try {
				const result = await initializeDefaults({});
				toast.success(result);
			} catch (error) {
				console.error("Fehler bei der Synchronisation:", error);
				toast.error(
					"Fehler bei der Synchronisation: " + (error as Error).message,
				);
			} finally {
				setIsSynchronizing(false);
			}
		}
	};

	// Synchronize button
	const actionButton = (
		<div className="flex items-center gap-2">
			<Button
				onClick={() => setShowLegend(!showLegend)}
				size="sm"
				variant="outline"
				className="gap-1"
				title={showLegend ? "Legende ausblenden" : "Legende anzeigen"}
			>
				<Info className="h-4 w-4" />
				{showLegend ? "Legende ausblenden" : "Legende anzeigen"}
			</Button>
			<Button
				onClick={handleSynchronize}
				size="sm"
				variant="outline"
				className="gap-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 border-blue-500/30 hover:border-blue-500/50"
				disabled={isSynchronizing}
			>
				<RefreshCw
					className={`h-4 w-4 ${isSynchronizing ? "animate-spin" : ""}`}
				/>
				{isSynchronizing ? "Synchronisiere..." : "Synchronisieren"}
			</Button>
		</div>
	);

	// Compact Legend Component
	const LegendSection = () => {
		if (!showLegend) return null;
		
		return (
			<Card className="mb-6 shadow-lg border-0 animate-in fade-in duration-200 overflow-hidden">
				<CardHeader className="pb-2 px-4 pt-3 border-b border-gray-800 bg-gray-800/40">
					<CardTitle className="text-base font-medium flex items-center gap-2">
						<Info className="h-4 w-4 text-blue-400" />
						Legende: Feldtypen und Eigenschaften
					</CardTitle>
				</CardHeader>
				<CardContent className="p-3">
					<div className="flex flex-col md:flex-row gap-4">
						{/* Left Column: Field Types */}
						<div className="flex-1 space-y-1 min-w-0">
							<p className="text-xs font-medium text-gray-300 mb-1 uppercase tracking-wider">
								Feldtypen
							</p>
							<div className="grid grid-cols-2 sm:grid-cols-3 gap-x-3 gap-y-1">
								{Object.entries(FIELD_TYPES).map(([key, value]) => (
									<div key={key} className="flex items-center min-w-0 text-xs">
										<span
											className={cn(
												"inline-block w-2.5 h-2.5 rounded-sm mr-1.5 border flex-shrink-0",
												fieldTypeColors[value as string] ||
													fieldTypeColors.default,
											)}
										></span>
										<span className="text-gray-300 truncate">
											{(value as string).charAt(0).toUpperCase() +
												(value as string).slice(1)}
										</span>
									</div>
								))}
							</div>
						</div>

						{/* Right Column: Field Properties */}
						<div className="flex-1 space-y-1 min-w-0 md:pl-4 md:border-l md:border-gray-700/50">
							<p className="text-xs font-medium text-gray-300 mb-1 uppercase tracking-wider">
								Feldeigenschaften
							</p>
							<div className="grid grid-cols-2 sm:grid-cols-3 gap-x-3 gap-y-1">
								{[
									{
										Icon: AlertTriangle,
										char: "R",
										text: "Erforderlich",
										colorClass: "bg-red-500/20 text-red-300 border-red-500/40",
									},
									{
										Icon: Copy,
										char: "K",
										text: "Kopierbar",
										colorClass:
											"bg-blue-500/20 text-blue-300 border-blue-500/40",
									},
									{
										Icon: EyeOff,
										char: "V",
										text: "Versteckt",
										colorClass:
											"bg-purple-500/20 text-purple-300 border-purple-500/40",
									},
									{
										Icon: Sparkles,
										char: "E",
										text: "Extrafeld",
										colorClass:
											"bg-green-500/20 text-green-300 border-green-500/40",
									},
									{
										Icon: ListChecks,
										char: "O",
										text: "Optionen",
										colorClass:
											"bg-indigo-500/20 text-indigo-300 border-indigo-500/40",
									},
									{
										Icon: MessageSquareText,
										char: "P",
										text: "Placeholder",
										colorClass:
											"bg-yellow-500/20 text-yellow-300 border-yellow-500/40",
									},
								].map((prop) => (
									<div key={prop.text} className="flex items-center min-w-0 text-xs">
										<span
											className={cn(
												"inline-flex items-center justify-center w-3.5 h-3.5 rounded-sm mr-1.5 border text-[10px] font-mono flex-shrink-0",
												prop.colorClass,
											)}
										>
											{prop.char}
										</span>
										<span className="text-gray-300 truncate">{prop.text}</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	};

	return (
		<PageLayout
			ref={pageTopRef}
			title="Dokumentationskategorien"
			subtitle="Verwalten Sie hier die Kategorien für die Kundendokumentation"
			action={actionButton}
		>
			{/* Compact Legend - conditionally shown */}
			<LegendSection />

			{/* Categories Card */}
			<Card className="shadow-lg border-0 overflow-hidden">
				<CardHeader className="pb-2 px-4 pt-3 border-b border-gray-800 bg-gray-800/40">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Database className="h-5 w-5 text-blue-400" />
						Kategorien
					</CardTitle>
				</CardHeader>
				<DokuKategorienTable
					kategorien={filteredKategorien.sort(
						(a, b) => a.reihenfolge - b.reihenfolge,
					)}
				/>
			</Card>
		</PageLayout>
	);
}

export default DokuKategorienPage;
