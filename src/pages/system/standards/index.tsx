import {
	<PERSON>,
	Card<PERSON>ontent,
	<PERSON><PERSON>eader,
} from "@/components/_shared/Card";
import { PageLayout } from "@/components/layout/PageLayout";
import { 
	Check, 
	X, 
	FileText, 
	<PERSON><PERSON>heck, 
	Settings, 
	ChevronDown, 
	ChevronUp,
	Mail
} from "lucide-react";
import { useEffect, useState } from "react";
import { defaultSettings } from "../../../../convex/system/standardsConfig";
import { defaultEmailTemplates } from "../../../../convex/system/emailConfig";
import {
	Ta<PERSON>,
	Ta<PERSON>Content,
	TabsList,
	TabsTrigger,
} from "@/components/_shared/Tabs";

type StandardsTab = "uebersicht" | "lieferschein" | "email";

export function StandardsPage() {
	// Get settings from the config file
	const uebersichtSettings = defaultSettings.uebersicht;
	const lieferscheinSettings = defaultSettings.lieferschein;
	const emailSettings = defaultSettings.email;
	
	// Get email templates (config would need backend call)
	const emailTemplates = defaultEmailTemplates;

	// State for selected tab
	const [selectedTab, setSelectedTab] = useState<StandardsTab>("uebersicht");

	// State for logo images
	const [uebersichtLogo, setUebersichtLogo] = useState<string | null>(null);
	const [lieferscheinLogo, setLieferscheinLogo] = useState<string | null>(null);

	// State for expandable text fields
	const [isLegalTextExpanded, setIsLegalTextExpanded] = useState(false);
	const [expandedEmailFields, setExpandedEmailFields] = useState<Record<string, boolean>>({});

	// Load logo images
	useEffect(() => {
		// Load Übersicht logo
		try {
			setUebersichtLogo(uebersichtSettings.logoPath);
		} catch (error) {
			console.error("Error loading Übersicht logo:", error);
		}

		// Load Lieferschein logo
		try {
			setLieferscheinLogo(lieferscheinSettings.logoPath);
		} catch (error) {
			console.error("Error loading Lieferschein logo:", error);
		}
	}, []);

	// Helper function to toggle email field expansion
	const toggleEmailFieldExpansion = (fieldName: string) => {
		setExpandedEmailFields(prev => ({
			...prev,
			[fieldName]: !prev[fieldName]
		}));
	};

	// Helper function to render boolean values with icons
	const renderBoolean = (value: boolean) => (
		<span className={value ? "text-green-400" : "text-red-400"}>
			{value ? (
				<Check className="inline-block h-4 w-4 mr-1" />
			) : (
				<X className="inline-block h-4 w-4 mr-1" />
			)}
			{value ? "Ja" : "Nein"}
		</span>
	);

	// Setting item component
	const SettingItem = ({ 
		label, 
		value, 
		isExpandable = false, 
		isExpanded = false,
		onToggleExpand = () => {} 
	}: { 
		label: string; 
		value: React.ReactNode; 
		isExpandable?: boolean;
		isExpanded?: boolean;
		onToggleExpand?: () => void;
	}) => {
		const isTextLong = typeof value === 'string' && value.length > 100;
		const shouldBeExpandable = isExpandable || isTextLong;

		return (
			<div className="py-1.5 border-b border-gray-800/40">
				<div className="flex justify-between items-center">
					<span className="text-sm font-medium text-blue-400 w-1/3">{label}</span>
					<div className="flex flex-1 justify-between items-center">
						<div className={`text-sm ${shouldBeExpandable && !isExpanded ? 'line-clamp-1' : ''}`}>
							{value}
						</div>
						{shouldBeExpandable && (
							<button 
								onClick={onToggleExpand}
								className="text-gray-400 hover:text-gray-300 ml-2 flex-shrink-0"
								aria-label={isExpanded ? "Collapse" : "Expand"}
							>
								{isExpanded ? (
									<ChevronUp className="h-4 w-4" />
								) : (
									<ChevronDown className="h-4 w-4" />
								)}
							</button>
						)}
					</div>
				</div>
			</div>
		);
	};

	return (
		<PageLayout
			title="Systemeinstellungen"
			subtitle="Übersicht der Standardeinstellungen für PDF-Dokumente"
		>
			<Card className="shadow-lg border-0 overflow-hidden">
				<Tabs
					value={selectedTab}
					onValueChange={(value) => setSelectedTab(value as StandardsTab)}
					className="w-full"
				>
					<CardHeader className="border-b border-gray-700/50 p-4">
						<div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3">
							<TabsList>
								<TabsTrigger value="uebersicht">
									<FileText className="h-4 w-4 mr-1.5" /> Übersichten
								</TabsTrigger>
								<TabsTrigger value="lieferschein">
									<FileCheck className="h-4 w-4 mr-1.5" /> Lieferscheine
								</TabsTrigger>
								<TabsTrigger value="email">
									<Mail className="h-4 w-4 mr-1.5" /> E-Mail
								</TabsTrigger>
							</TabsList>
							<div className="text-xs text-gray-400 flex items-center gap-1.5">
								<Settings className="h-3 w-3" />
								Dokumenteinstellungen
							</div>
						</div>
					</CardHeader>

					{/* Übersichten Tab Content */}
					<TabsContent value="uebersicht" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col">
								{/* Logo at the top */}
								{uebersichtLogo && (
									<div className="mb-6 flex justify-center">
										<div className="relative max-w-full max-h-28">
											<img
												src={uebersichtLogo}
												alt="Übersicht Logo"
												className="max-h-28 object-contain"
												onError={(e) => {
													console.error("Error loading image");
													e.currentTarget.src =
														"https://via.placeholder.com/200x100?text=Logo+nicht+gefunden";
												}}
											/>
											{!uebersichtSettings.showLogo && (
												<div className="absolute inset-0 bg-black/60 flex items-center justify-center">
													<span className="text-white text-xs px-2 py-0.5 bg-red-500/80 rounded">
														Deaktiviert
													</span>
												</div>
											)}
										</div>
									</div>
								)}

								{/* Settings in vertical layout, one per row */}
								<div className="flex flex-col space-y-1.5">
									{/* General Settings */}
									<SettingItem 
										label="logoPath" 
										value={uebersichtSettings.logoPath}
									/>
									
									<SettingItem 
										label="fusszeileText" 
										value={uebersichtSettings.fusszeileText} 
									/>
									
									<SettingItem 
										label="includeHeader" 
										value={renderBoolean(uebersichtSettings.includeHeader)} 
									/>
									
									<SettingItem 
										label="showLogo" 
										value={renderBoolean(uebersichtSettings.showLogo)} 
									/>
									
									<SettingItem 
										label="includeFooter" 
										value={renderBoolean(uebersichtSettings.includeFooter)} 
									/>
									
									<SettingItem 
										label="includeLeistungsuebersicht" 
										value={renderBoolean(uebersichtSettings.includeLeistungsuebersicht)} 
									/>
									
									<SettingItem 
										label="includeKontingentuebersicht" 
										value={renderBoolean(uebersichtSettings.includeKontingentuebersicht)} 
									/>
									
									<SettingItem 
										label="includeSummary" 
										value={renderBoolean(uebersichtSettings.includeSummary)} 
									/>
								</div>
							</div>
						</CardContent>
					</TabsContent>

					{/* Lieferscheine Tab Content */}
					<TabsContent value="lieferschein" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col">
								{/* Logo at the top */}
								{lieferscheinLogo && (
									<div className="mb-6 flex justify-center">
										<div className="relative max-w-full max-h-28">
											<img
												src={lieferscheinLogo}
												alt="Lieferschein Logo"
												className="max-h-28 object-contain"
												onError={(e) => {
													console.error("Error loading image");
													e.currentTarget.src =
														"https://via.placeholder.com/200x100?text=Logo+nicht+gefunden";
												}}
											/>
											{!lieferscheinSettings.showLogo && (
												<div className="absolute inset-0 bg-black/60 flex items-center justify-center">
													<span className="text-white text-xs px-2 py-0.5 bg-red-500/80 rounded">
														Deaktiviert
													</span>
												</div>
											)}
										</div>
									</div>
								)}

								{/* Settings in vertical layout, one per row */}
								<div className="flex flex-col space-y-1.5">
									{/* General Settings */}
									<SettingItem 
										label="logoPath" 
										value={lieferscheinSettings.logoPath} 
									/>
									
									<SettingItem 
										label="fusszeileText" 
										value={lieferscheinSettings.fusszeileText} 
									/>
									
									<SettingItem 
										label="signatureText" 
										value={lieferscheinSettings.signatureText} 
									/>
									
									<SettingItem 
										label="includeHeader" 
										value={renderBoolean(lieferscheinSettings.includeHeader)} 
									/>
									
									<SettingItem 
										label="showLogo" 
										value={renderBoolean(lieferscheinSettings.showLogo)} 
									/>
									
									<SettingItem 
										label="includeFooter" 
										value={renderBoolean(lieferscheinSettings.includeFooter)} 
									/>
									
									<SettingItem 
										label="includeSignatureField" 
										value={renderBoolean(lieferscheinSettings.includeSignatureField)} 
									/>
									
									{/* Legal text with expand/collapse functionality */}
									<SettingItem 
										label="legalText" 
										value={lieferscheinSettings.legalText}
										isExpandable={true}
										isExpanded={isLegalTextExpanded}
										onToggleExpand={() => setIsLegalTextExpanded(!isLegalTextExpanded)}
									/>
								</div>
							</div>
						</CardContent>
					</TabsContent>
					
					{/* Email Tab Content */}
					<TabsContent value="email" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col space-y-4">
								{/* Email Configuration Section */}
								<div className="border-b border-gray-700/50 pb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Settings className="h-5 w-5" />
										TurboSMTP Konfiguration
									</h3>
									<div className="flex flex-col space-y-1.5">
										<SettingItem 
											label="TURBOSMTP_CONSUMER_KEY" 
											value="Wird über Convex Environment Variables geladen" 
										/>
										
										<SettingItem 
											label="TURBOSMTP_CONSUMER_SECRET" 
											value="Wird über Convex Environment Variables geladen" 
										/>
										
										<SettingItem 
											label="EMAIL_FROM" 
											value="Wird über Convex Environment Variables geladen" 
										/>
										
										<SettingItem 
											label="EMAIL_FROM_NAME" 
											value="Wird über Convex Environment Variables geladen" 
										/>
										
										<SettingItem 
											label="TURBOSMTP_API_URL" 
											value="https://api.turbo-smtp.com/api/v2/mail/send (Standard)" 
										/>
									</div>
								</div>

								{/* Default Behavior Section */}
								<div className="border-b border-gray-700/50 pb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Check className="h-5 w-5" />
										Standard-Versandoptionen
									</h3>
									<div className="flex flex-col space-y-1.5">
										<SettingItem 
											label="defaultSendToMitarbeiter" 
											value={renderBoolean(emailSettings.defaultSendToMitarbeiter)} 
										/>
										
										<SettingItem 
											label="defaultSendToKunde" 
											value={renderBoolean(emailSettings.defaultSendToKunde)} 
										/>
									</div>
								</div>

								{/* Email Templates Section */}
								<div>
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Templates
									</h3>
									<div className="flex flex-col space-y-1.5">
										{/* Lieferschein Template */}
										<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
											<h4 className="text-sm font-medium text-gray-300 mb-2">Lieferschein Template</h4>
											
											<SettingItem 
												label="subject" 
												value={emailTemplates.lieferschein.subject} 
											/>
											
											<SettingItem 
												label="body" 
												value={emailTemplates.lieferschein.body} 
												isExpandable={true}
												isExpanded={expandedEmailFields['lieferschein-body'] || false}
												onToggleExpand={() => toggleEmailFieldExpansion('lieferschein-body')}
											/>
										</div>

										{/* Übersicht Template */}
										<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
											<h4 className="text-sm font-medium text-gray-300 mb-2">Übersicht Template</h4>
											
											<SettingItem 
												label="subject" 
												value={emailTemplates.uebersicht.subject} 
											/>
											
											<SettingItem 
												label="body" 
												value={emailTemplates.uebersicht.body} 
												isExpandable={true}
												isExpanded={expandedEmailFields['uebersicht-body'] || false}
												onToggleExpand={() => toggleEmailFieldExpansion('uebersicht-body')}
											/>
										</div>

									</div>
								</div>
							</div>
						</CardContent>
					</TabsContent>
				</Tabs>
			</Card>
		</PageLayout>
	);
}

export default StandardsPage;
