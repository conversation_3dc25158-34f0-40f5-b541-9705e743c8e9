import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { PageLayout } from "@/components/layout/PageLayout";
import { MitarbeiterPageForm } from "@/components/verwaltung/mitarbeiter/MitarbeiterPageForm";
import { useMutation, useQuery } from "convex/react";
import { ArrowLeft, Save, Trash2 } from "lucide-react";
import { useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

export function MitarbeiterDetailPage() {
	const { id } = useParams<{ id: string }>();
	const navigate = useNavigate();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Convert id to proper type
	const mitarbeiterId = id as Id<"mitarbeiter">;

	// Get employee data
	const mitarbeiter = useQuery(api.verwaltung.mitarbeiter.get, {
		id: mitarbeiterId,
	});

	// Mutations
	const updateMitarbeiter = useMutation(api.verwaltung.mitarbeiter.update);
	const deleteMitarbeiter = useMutation(api.verwaltung.mitarbeiter.delete_);

	const handleSave = async (data: any) => {
		if (!mitarbeiter) return;

		setIsSubmitting(true);
		try {
			await updateMitarbeiter({
				id: mitarbeiter._id,
				...data,
			});
			toast.success("Mitarbeiter erfolgreich aktualisiert");
			navigate("/verwaltung/mitarbeiter");
		} catch (error: any) {
			toast.error(`Fehler beim Speichern: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleDelete = async () => {
		if (!mitarbeiter) return;

		if (
			!confirm("Sind Sie sicher, dass Sie diesen Mitarbeiter löschen möchten?")
		) {
			return;
		}

		setIsSubmitting(true);
		try {
			await deleteMitarbeiter({ id: mitarbeiter._id });
			toast.success("Mitarbeiter erfolgreich gelöscht");
			navigate("/verwaltung/mitarbeiter");
		} catch (error: any) {
			toast.error(`Fehler beim Löschen: ${error.message}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	if (!mitarbeiter) {
		return (
			<PageLayout title="Mitarbeiter wird geladen..." subtitle="Bitte warten">
				<div className="flex items-center justify-center h-64">
					<div className="text-gray-400">Lade Mitarbeiterdaten...</div>
				</div>
			</PageLayout>
		);
	}

	const headerActions = (
		<div className="flex items-center gap-2">
			<Link to="/verwaltung/mitarbeiter">
				<Button variant="outline" className="gap-2">
					<ArrowLeft className="h-4 w-4" />
					Zurück
				</Button>
			</Link>

			<Button
				variant="destructive"
				onClick={handleDelete}
				disabled={isSubmitting}
				className="gap-2"
			>
				<Trash2 className="h-4 w-4" />
				Löschen
			</Button>

			<Button
				form="mitarbeiter-form"
				type="submit"
				disabled={isSubmitting}
				className="gap-2"
			>
				<Save className="h-4 w-4" />
				{isSubmitting ? "Speichert..." : "Speichern"}
			</Button>
		</div>
	);

	return (
		<PageLayout
			title={`Mitarbeiter bearbeiten: ${mitarbeiter.name}`}
			subtitle="Mitarbeiterdaten bearbeiten"
			action={headerActions}
		>
			<div className="max-w-4xl mx-auto">
				{/* Form */}
				<MitarbeiterPageForm
					initialData={mitarbeiter}
					onSubmit={handleSave}
					isSubmitting={isSubmitting}
					formId="mitarbeiter-form"
				/>
			</div>
		</PageLayout>
	);
}

export default MitarbeiterDetailPage;
