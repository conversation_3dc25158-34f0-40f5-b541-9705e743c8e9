import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Card, CardHeader, CardTitle } from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { EmptyState } from "@/components/layout/EmptyState"; // Import new EmptyState
import { PageLayout } from "@/components/layout/PageLayout";
import { MitarbeiterDataTable } from "@/components/verwaltung/mitarbeiter/MitarbeiterDataTable";
import { MitarbeiterFilterControls } from "@/components/verwaltung/mitarbeiter/MitarbeiterFilterControls";
import { MitarbeiterForm } from "@/components/verwaltung/mitarbeiter/MitarbeiterForm";
import { useMutation, useQuery } from "convex/react";
import {
	Pencil,
	PlusCircle,
	Search,
	Trash2,
	UserCircle,
	X,
} from "lucide-react";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";
import { toast } from "sonner";

interface Mitarbeiter {
	_id: Id<"mitarbeiter">;
	name: string;
	email: string;
}

export function MitarbeiterPage() {
	const mitarbeiter = useQuery(api.verwaltung.mitarbeiter.list) || [];
	const removeMitarbeiter = useMutation(api.verwaltung.mitarbeiter.remove);

	const [editingId, setEditingId] = useState<Id<"mitarbeiter"> | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");

	const editingMitarbeiter = useMemo(() => {
		return editingId
			? (mitarbeiter.find((m) => m._id === editingId) ?? null)
			: null;
	}, [editingId, mitarbeiter]);

	const handleFormSubmitSuccess = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleFormCancel = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleEdit = (m: Mitarbeiter) => {
		setEditingId(m._id);
		setShowForm(true);
	};

	const handleDelete = async (id: Id<"mitarbeiter">) => {
		if (window.confirm("Möchten Sie diesen Mitarbeiter wirklich löschen?")) {
			try {
				await removeMitarbeiter({ id });
				toast.success("Mitarbeiter erfolgreich gelöscht");
				if (id === editingId) {
					handleFormCancel();
				}
			} catch (error) {
				toast.error(
					`Fehler beim Löschen: ${String((error as Error).message || error)}`,
				);
			}
		}
	};

	const filteredMitarbeiter = useMemo(
		() =>
			mitarbeiter.filter(
				(m) =>
					m.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					m.email.toLowerCase().includes(searchTerm.toLowerCase()),
			),
		[mitarbeiter, searchTerm],
	);

	const handleSearchTermChange = (value: string) => {
		setSearchTerm(value);
	};

	const resetFilters = () => {
		setSearchTerm("");
	};

	const actionButton = showForm ? (
		<Button
			onClick={handleFormCancel}
			size="sm"
			className="gap-1"
		>
			<X className="h-4 w-4" /> Abbrechen
		</Button>
	) : (
		<Link to="/verwaltung/mitarbeiter/neu">
			<Button size="sm" className="gap-1">
				<PlusCircle className="h-4 w-4" /> Neuer Mitarbeiter
			</Button>
		</Link>
	);

	return (
		<PageLayout
			title="Mitarbeiter"
			subtitle="Verwalten Sie hier Ihre Mitarbeiterdaten"
			action={actionButton}
		>
			{showForm && (
				<MitarbeiterForm
					editingMitarbeiter={editingMitarbeiter}
					onSubmitSuccess={handleFormSubmitSuccess}
					onCancel={handleFormCancel}
				/>
			)}

			<Card className="shadow-lg border-0 overflow-hidden">
				<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
					<div className="flex flex-wrap items-center justify-between gap-4">
						<CardTitle className="text-lg font-medium">
							Mitarbeiterübersicht
						</CardTitle>
						<MitarbeiterFilterControls
							searchTerm={searchTerm}
							onSearchTermChange={handleSearchTermChange}
							itemCount={filteredMitarbeiter.length}
						/>
					</div>
				</CardHeader>

				{filteredMitarbeiter.length === 0 ? (
					<EmptyState
						icon={<UserCircle className="w-12 h-12" />} // Adjusted icon size
						title="Keine Mitarbeiter gefunden"
						message={
							searchTerm
								? "Versuchen Sie, Ihre Suche anzupassen"
								: "Erstellen Sie einen neuen Mitarbeiter mit dem Button oben rechts"
						}
						actions={
							searchTerm && (
								<Button variant="outline" size="sm" onClick={resetFilters}>
									Filter zurücksetzen
								</Button>
							)
						}
					/>
				) : (
					<MitarbeiterDataTable
						mitarbeiter={filteredMitarbeiter}
						onEdit={handleEdit}
						onDelete={handleDelete}
					/>
				)}
			</Card>
		</PageLayout>
	);
}

export default MitarbeiterPage;
